using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

public class HeroSkillView : ScaleSprite
{

    public const string SKILL_PIC = "S_0_S";

    public const string XSKILL_PIC = "S_0_X";

    public const string CLICK_TYPE = "SKILL";

    private static readonly List<string> heroTipRestraint = new() { "", LanguageManager.getScene("TIP_HE_LEVEL_10_REEL"), LanguageManager.getScene("TIP_HE_LEVEL_20_REEL"), LanguageManager.getScene("TIP_HE_LEVEL_30_REEL"), LanguageManager.getScene("TIP_HE_LEVEL_40_REEL") };

    private static readonly List<List<int>> heroRestraint = new() { new() { 0, 0 }, new() { 1, 10 }, new() { 2, 20 }, new() { 3, 30 }, new() { 4, 40 } };

    public GameObject skill_box;

    public GameObject keel_box;

    private HeroVO hero_vo;

    private int hero_id;

    private List<int> now_skill;

    private List<List<int>> now_keel;

    private int new_id;

    private GoldBorderUILoader new_link;

    private int count = 0;

    public List<int> updateAry;

    public int unloadIndex;

    public bool editable = true;

    private GoldBorderUILoader now_click;

    public int action_id = 0;

    public int now_type = 0;

    //   private var keelMap:Map;

    //   private var skillTM:Map;

    public UnityEvent CLICK_TYPE_SKILL = new();

    protected override void Awake()
    {
        base.Awake();
    }

    private void creatUI()
    {

    }
    public void setHeroVO(HeroVO _vo)
    {
        if (_vo != null)
        {
            hero_vo = _vo;
            parseHeroVOSkill();
            parseHeroVOKeel();
        }
    }

    private void parseHeroVOSkill()
    {
        int i = 0;
        int j = 0;
        if (hero_vo.skills == null)
        {
            now_skill = new();
        }
        else
        {
            if (hero_vo.targetId == hero_id)
            {
                if (ListUtils.Compare(now_skill, hero_vo.skills))
                {
                    return;
                }
                for (j = 0; j < hero_vo.skills.Count; j++)
                {
                    if (now_skill == null)
                    {
                        break;
                    }
                    if (now_skill.Count > hero_vo.skills.Count)
                    {
                        break;
                    }
                    if (now_skill[j] != hero_vo.skills[j])
                    {
                        new_id = hero_vo.skills[j];
                        break;
                    }
                }
            }
            else
            {
                hero_id = hero_vo.targetId;
            }
            now_skill = new();
            for (i = 0; i < hero_vo.skills.Count; i++)
            {
                now_skill.Add(hero_vo.skills[i]);
            }
        }
        showSkill().Forget();
    }

    private async UniTaskVoid showSkill()
    {
        GoldBorderUILoader tempUILoader = null;
        //  if(skillTM == null)
        //  {
        //     return;
        //  }
        //  TweenLite.killTweensOf(new_link);
        for (int i = 0; i < 5; i++)
        {
            tempUILoader = skill_box.transform.GetChild(i).GetComponent<GoldBorderUILoader>();
            if (i < now_skill.Count)
            {
                tempUILoader.source = await ResPathManager.getSkillIcon(SkillNameUtils.getOneSkillLevel(now_skill[i]), ResPathManager.RES_SIZE_M);
                //    tempUILoader.filters = null;
                ToolTipManager.getInstance().regTarget(tempUILoader.gameObject, getToolTipContent(now_skill[i]));
                tempUILoader.buttonMode = true;
                tempUILoader.useHandCursor = true;
                addBorderListener(tempUILoader);
                if (new_id == now_skill[i])
                {
                    new_link = tempUILoader;
                    count = 0;
                    // attackFilterRunBack();
                    new_id = 0;
                }
            }
            else
            {
                tempUILoader.source = await ResPathManager.getMenuIcon(SKILL_PIC);
                tempUILoader.buttonMode = false;
                tempUILoader.useHandCursor = false;
                // tempUILoader.filters = null;
                ToolTipManager.getInstance().unRegTarget(tempUILoader.gameObject);
                removeBorderListener(tempUILoader);
                tempUILoader.OnClick_UI.RemoveListener((sp) =>
                {
                    clickHandler(sp);
                });
            }
        }
    }
    private string getToolTipContent(int _id)
    {
        string returnStr = "";
        return LanguageManager.getScene("LABEL_TM_NAME") + "：" + SkillNameUtils.getHeroSkillName(_id) + "\n" + LanguageManager.getScene("LABEL_TM_LEVEL") + "：" + SkillNameUtils.getskillLevel(_id) + "\n" + LanguageManager.getScene("LABEL_TM_DESCRIBE") + "：" + SkillProxy.skillInfo_map[_id].intro;
    }
    private void parseHeroVOKeel()
    {
        int i = 0;
        int j = 0;
        if (hero_vo.keels == null)
        {
            now_keel = new();
        }
        else
        {
            if (hero_vo.targetId == hero_id)
            {
                if (ListUtils.Compare(now_keel, hero_vo.keels))
                {
                    return;
                }
                for (j = 0; j < hero_vo.keels.Count; j++)
                {
                    if (now_keel == null)
                    {
                        break;
                    }
                    if (now_keel.Count > hero_vo.keels.Count)
                    {
                        break;
                    }
                    if (now_keel[j] != null && hero_vo.keels[j] != null)
                    {
                        if (now_keel[j][0] != hero_vo.keels[j][0])
                        {
                            new_id = now_keel[j][0];
                            break;
                        }
                    }
                }
            }
            else
            {
                hero_id = hero_vo.targetId;
            }
            now_keel = new();
            for (i = 0; i < hero_vo.keels.Count; i++)
            {
                now_keel.Add(hero_vo.keels[i]);
            }
        }
        showKeel().Forget();
    }

    private async UniTaskVoid showKeel()
    {
        GoldBorderUILoader tempUILoader = null;
        // int j = 0;
        int id = 0;
        int num = 0;
        // CommonTextMeshPro txt = null;
        int length = 0;
        int lengthNum = 0;
        //  TweenLite.killTweensOf(new_link);
        for (int i = 0; i < 5; i++)
        {
            tempUILoader = keel_box.transform.GetChild(i).GetComponent<GoldBorderUILoader>();
            tempUILoader.txt.gameObject.SetActive(false);
            // for(j = 0; j < tempUILoader.numChildren; j++)
            // {
            //    if(tempUILoader.getChildAt(j) is TextField)
            //    {
            //       tempUILoader.removeChildAt(j);
            //    }
            // }
            if (i < now_keel.Count)
            {
                id = now_keel[i][0];
                num = now_keel[i][1];
                tempUILoader.source = ResPathManager.getPropIcon(id, ResPathManager.RES_SIZE_M);
                //    tempUILoader.filters = null;
                ToolTipManager.getInstance().regTarget(tempUILoader.gameObject, getKeelTip(id));
                //    txt = new TextField();
                //    txt.defaultTextFormat = new TextFormat(LanguageManager.getScene("GAME_FONT"),11,2668581);
                //    txt.filters = [FilterEffectUtils.getPixelOuterBorderFilter(0,0.8)];
                tempUILoader.txt.text = num.ToString();
                length = num / 10;
                lengthNum = 0;
                while (length != 0)
                {
                    length /= 10;
                    lengthNum++;
                }
                tempUILoader.txt.x = (float)(21 - lengthNum * 5.5);
                tempUILoader.txt.y = -18;
                tempUILoader.txt.height = 14;
                lengthNum = 0;
                tempUILoader.txt.gameObject.SetActive(true);
                tempUILoader.buttonMode = true;
                tempUILoader.useHandCursor = true;
                addBorderListenerKeel(tempUILoader);
            }
            else
            {
                ToolTipManager.getInstance().unRegTarget(tempUILoader.gameObject);
                if (heroRestraint[i][1] <= hero_vo.rating)
                {
                    tempUILoader.source = await ResPathManager.getMenuIcon(SKILL_PIC);
                }
                else
                {
                    tempUILoader.source = await ResPathManager.getMenuIcon(XSKILL_PIC);
                    ToolTipManager.getInstance().regTarget(tempUILoader.gameObject, heroTipRestraint[i]);
                }
                tempUILoader.buttonMode = false;
                tempUILoader.useHandCursor = false;
                // tempUILoader.filters = null;
                removeBorderListenerKeel(tempUILoader);
                tempUILoader.OnClick_UI.RemoveListener((sp) =>
                {
                    clickHandler(sp);
                });
            }
        }
    }

    private void addBorderListenerKeel(GoldBorderUILoader _sp)
    {
        if (editable)
        {
            _sp.OnClick_UI.AddListener((sp) =>
            {
                clickKeelHandler(sp);
            });
        }
    }

    private void removeBorderListenerKeel(GoldBorderUILoader _sp)
    {
        _sp.OnClick_UI.RemoveListener((sp) =>
           {
               clickKeelHandler(sp);
           });
    }

    private void addBorderListener(GoldBorderUILoader _sp)
    {
        if (editable)
        {
            _sp.OnClick_UI.AddListener((sp) =>
            {
                clickHandler(sp);
            });
        }
    }

    private void removeBorderListener(GoldBorderUILoader _sp)
    {
        _sp.OnClick_UI.RemoveListener((sp) =>
          {
              clickHandler(sp);
          });
    }

    private void clickHandler(GoldBorderUILoader evt)
    {
        int i = 0;
        now_type = 0;
        if (checkID(evt))
        {
            now_type = 0;
            updateAry = new();
            for (i = 0; i < now_skill.Count; i++)
            {
                if (now_skill[i] != action_id)
                {
                    updateAry.Add(now_skill[i]);
                }
                else
                {
                    unloadIndex = i;
                    updateAry.Add(0);
                }
            }
            ToolTipManager.getInstance().removeToolTip();
            CLICK_TYPE_SKILL.Invoke();
        }
    }

    private void clickKeelHandler(GoldBorderUILoader e)
    {
        now_type = 1;
        if (checkID(e))
        {
            ToolTipManager.getInstance().removeToolTip();
            CLICK_TYPE_SKILL.Invoke();
        }
    }

    private bool checkID(GoldBorderUILoader _target)
    {
        int id = 0;
        int i = 0;
        action_id = 0;
        bool b = false;
        if (now_type == 0)
        {
            for (i = 0; i < skill_box.transform.childCount; i++)
            {
                if (_target == skill_box.transform.GetChild(i).GetComponent<GoldBorderUILoader>())
                {
                    action_id = now_skill[i];
                    b = true;
                    break;
                }
            }
        }
        else if (now_type == 1)
        {
            for (i = 0; i < keel_box.transform.childCount; i++)
            {
                if (_target == keel_box.transform.GetChild(i).GetComponent<GoldBorderUILoader>())
                {
                    action_id = now_keel[i][0];
                    b = true;
                    break;
                }
            }
        }
        return b;
    }
    private string getKeelTip(int _id)
    {
        if (GoodsProxy.goodInfo_map.ContainsKey(_id))
        {
            return GoodsProxy.goodInfo_map[_id].name + "\n" + GoodsProxy.goodInfo_map[_id].intro;
        }
        return "";
    }
    public void setkeelMap()
    {
        //  if(keelMap == null)
        //  {
        //     keelMap = _keelMap;
        //     if(now_keel != null)
        //     {
        //        showKeel();
        //     }
        //  }
    }

    public void onRemove()
    {

    }
}