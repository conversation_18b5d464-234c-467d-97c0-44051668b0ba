using Pb;
using UnityEngine;

public class SocketProxy
{
    // private static SocketProxy instance;

    // 定义 action 常量
    public const string EVENT_RECEIVE_MSG = "EVENT_RECEIVE_MSG";
    public const int SMTS_WELCOME_MSG = 1;
    public const int CMTS_WELCOME_MSG = 1;
    public const int SMTS_USER_LOGIN = 2;
    public const int CMTS_LOGIN = 2;

    public const int CMTS_OBJECT_PROPERTY = 4;
    public const int CMTR_OBJECT_PROPERTY = 4;

    public const int CMTS_PLAYER_EQUIPMENT = 5;
    public const int CMTR_PLAYER_EQUIPMENT = 5;

    public const int CMTS_CLASS_PROPERTY = 9;
    public const int CMTR_CLASS_PROPERTY = 9;
    public const int CMTS_CHAT_WORLD = 12;
    public const int CMTR_CHAT_WORLD_RESULT = 12;
    public const int CMTR_CHAT_WORLD = 15;
    public const int CMTS_RACE_BUILD_LIST = 28;
    public const int CMTR_RACE_BUILD_LIST_RESULT = 28;
    public const int CMTS_HERO_EQUIPMENT = 34;
    public const int CMTR_HERO_EQUIPMENT = 34;
    public const int CMTS_HERO_ADD_SKILL = 36;
    public const int CMTR_HERO_ADD_SKILL = 36;
    public const int CMTS_HEROJN = 471;
    public const int CMTR_HEROJN = 471;
    public const int CMTR_LOAD_PLAYER_GOODS = 484;
    public const int CMTS_LOAD_PLAYER_GOODS = 484;

    public const string MSG_CUSTOM_CHAT_PERSONAL_RESULT = "MSG_CUSTOM_CHAT_PERSONAL_RESULT";

    public const string MSG_CUSTOM_OBJECT_PROPERTY = "MSG_CUSTOM_OBJECT_PROPERTY";

    public const string MSG_CUSTOM_MAP_INFO = "MSG_CUSTOM_MAP_INFO";

    public const string MSG_CUSTOM_MAP_OBJECT = "MSG_CUSTOM_MAP_OBJECT";

    public const string MSG_CUSTOM_MEMORYMAP_INFO = "MSG_CUSTOM_MEMORYMAP_INFO";

    public const string MSG_CUSTOM_VIE_MSG = "MSG_CUSTOM_VIE_MSG";

    public const string MSG_CUSTOM_VIEW_GM_MSG = "MSG_CUSTOM_VIEW_GM_MSG";

    public const string MSG_CUSTOM_NEW_GET_SCATTERED_MAP_DATA = "MSG_CUSTOM_NEW_GET_SCATTERED_MAP_DATA";

    public const string MSG_CUSTOM_NEW_GET_MAP_DATA = "MSG_CUSTOM_NEW_GET_MAP_DATA";

    public const string MSG_CUSTOM_NEW_GET_MAP_OBJECT = "MSG_CUSTOM_NEW_GET_MAP_OBJECT";

    public const string MSGC_RACE_BUILD_LIST_RESULT = "MSGC_RACE_BUILD_LIST_RESULT";

    public const string MSGC_BARRACKS_LIST_RESULT = "MSGC_BARRACKS_LIST_RESULT";

    public const string MSGC_TRAININGQUEUE_RESULT = "MSGC_TRAININGQUEUE_RESULT";

    public const string MSGC_TRAININGQUEUE_ADD_RESULT = "MSGC_TRAININGQUEUE_ADD_RESULT";

    public const string NAME = "SocketProxy";

    // ...

    // public static SocketProxy Instance
    // {
    //     get
    //     {
    //         instance ??= new SocketProxy();
    //         return instance;
    //     }
    // }

    public void HandleMessage(Message message)
    {
        switch (message.Action)
        {
            case SMTS_WELCOME_MSG:
                // Debug.Log(message.WelcomeMsg.Content);
                //Action1Handler.HandleAction1(message.Data);
                break;
            case SMTS_USER_LOGIN:
                LoginManager.Instance.LoginResultHandler(message.LoginRes);
                break;
            case CMTR_OBJECT_PROPERTY:
                ProxyManager.Instance.objectPropertyProxy.formatObjectData(message.ObjectPropertyRes, ObjectPropertyProxy.GAMEOBJECT_TYPE_OBJECT);
                break;
            case CMTR_CLASS_PROPERTY:
                ProxyManager.Instance.objectPropertyProxy.formatObjectData(message.ObjectPropertyRes, ObjectPropertyProxy.GAMEOBJECT_TYPE_CLASS);
                break;
            case CMTR_LOAD_PLAYER_GOODS:
                ProxyManager.Instance.goodsProxy.forgetPlayerGoods(message.PlayerGoodsRes);
                break;
            case CMTR_CHAT_WORLD_RESULT:
                ProxyManager.Instance.chatProxy.sendWorldChatResult(message.ChatWorldResultRes);
                break;
            case CMTR_CHAT_WORLD:
                ProxyManager.Instance.chatProxy.receiveWorldChatMsg(message.ChatWorldRes);
                break;
            case CMTR_PLAYER_EQUIPMENT:
                ProxyManager.Instance.goodsProxy.formatPlayerEquipment(message.PlayerEquipmentRes);
                break;
            case CMTR_HEROJN:
                ProxyManager.Instance.equipmentProxy.getHeroData(message.HeroJNRes);
                break;
            default:
                Debug.LogWarning("未知的消息 action: " + message.Action);
                break;
        }
    }
}
