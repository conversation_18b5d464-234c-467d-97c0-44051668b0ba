{"version": 3, "targets": {".NETStandard,Version=v2.1": {"ChocDino.UIFX/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/ChocDino.UIFX.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.dll": {}}}, "ChocDino.UIFX.Demos/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"ChocDino.UIFX": "1.0.0"}, "compile": {"bin/placeholder/ChocDino.UIFX.Demos.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.Demos.dll": {}}}, "ChocDino.UIFX.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"ChocDino.UIFX": "1.0.0"}, "compile": {"bin/placeholder/ChocDino.UIFX.Editor.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.Editor.dll": {}}}, "ChocDino.UIFX.TMP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"ChocDino.UIFX": "1.0.0"}, "compile": {"bin/placeholder/ChocDino.UIFX.TMP.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.TMP.dll": {}}}, "ChocDino.UIFX.TMP.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"ChocDino.UIFX": "1.0.0", "ChocDino.UIFX.Editor": "1.0.0", "ChocDino.UIFX.TMP": "1.0.0"}, "compile": {"bin/placeholder/ChocDino.UIFX.TMP.Editor.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.TMP.Editor.dll": {}}}, "ChocDino.UIFX.UITK/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"ChocDino.UIFX": "1.0.0"}, "compile": {"bin/placeholder/ChocDino.UIFX.UITK.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.UITK.dll": {}}}, "ChocDino.UIFX.UITK.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"ChocDino.UIFX": "1.0.0", "ChocDino.UIFX.Editor": "1.0.0", "ChocDino.UIFX.UITK": "1.0.0"}, "compile": {"bin/placeholder/ChocDino.UIFX.UITK.Editor.dll": {}}, "runtime": {"bin/placeholder/ChocDino.UIFX.UITK.Editor.dll": {}}}, "DOTween.Modules/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/DOTween.Modules.dll": {}}, "runtime": {"bin/placeholder/DOTween.Modules.dll": {}}}, "DOTweenPro.Scripts/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"DOTween.Modules": "1.0.0"}, "compile": {"bin/placeholder/DOTweenPro.Scripts.dll": {}}, "runtime": {"bin/placeholder/DOTweenPro.Scripts.dll": {}}}, "OSA.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/OSA.Core.dll": {}}, "runtime": {"bin/placeholder/OSA.Core.dll": {}}}, "OSA.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"OSA.Core": "1.0.0"}, "compile": {"bin/placeholder/OSA.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/OSA.Core.Editor.dll": {}}}, "OSA.Demos/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"OSA.Core": "1.0.0", "OSA.Utilities": "1.0.0"}, "compile": {"bin/placeholder/OSA.Demos.dll": {}}, "runtime": {"bin/placeholder/OSA.Demos.dll": {}}}, "OSA.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"OSA.Core": "1.0.0"}, "compile": {"bin/placeholder/OSA.Utilities.dll": {}}, "runtime": {"bin/placeholder/OSA.Utilities.dll": {}}}, "OSA.Utilities.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"OSA.Core": "1.0.0", "OSA.Core.Editor": "1.0.0", "OSA.Utilities": "1.0.0"}, "compile": {"bin/placeholder/OSA.Utilities.Editor.dll": {}}, "runtime": {"bin/placeholder/OSA.Utilities.Editor.dll": {}}}, "UniTask/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UniTask.dll": {}}, "runtime": {"bin/placeholder/UniTask.dll": {}}}, "UniTask.Addressables/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.Addressables.dll": {}}, "runtime": {"bin/placeholder/UniTask.Addressables.dll": {}}}, "UniTask.DOTween/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"DOTween.Modules": "1.0.0", "UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.DOTween.dll": {}}, "runtime": {"bin/placeholder/UniTask.DOTween.dll": {}}}, "UniTask.Linq/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.Linq.dll": {}}, "runtime": {"bin/placeholder/UniTask.Linq.dll": {}}}, "UniTask.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/UniTask.TextMeshPro.dll": {}}}, "UnityUIExtensions/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityUIExtensions.dll": {}}, "runtime": {"bin/placeholder/UnityUIExtensions.dll": {}}}, "UnityUIExtensions.editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityUIExtensions": "1.0.0"}, "compile": {"bin/placeholder/UnityUIExtensions.editor.dll": {}}, "runtime": {"bin/placeholder/UnityUIExtensions.editor.dll": {}}}, "UnityUIExtensions.examples/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityUIExtensions": "1.0.0"}, "compile": {"bin/placeholder/UnityUIExtensions.examples.dll": {}}, "runtime": {"bin/placeholder/UnityUIExtensions.examples.dll": {}}}, "UnityWebSocket.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityWebSocket.Runtime": "1.0.0"}, "compile": {"bin/placeholder/UnityWebSocket.Editor.dll": {}}, "runtime": {"bin/placeholder/UnityWebSocket.Editor.dll": {}}}, "UnityWebSocket.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityWebSocket.Runtime.dll": {}}, "runtime": {"bin/placeholder/UnityWebSocket.Runtime.dll": {}}}}}, "libraries": {"ChocDino.UIFX/1.0.0": {"type": "project", "path": "ChocDino.UIFX.csproj", "msbuildProject": "ChocDino.UIFX.csproj"}, "ChocDino.UIFX.Demos/1.0.0": {"type": "project", "path": "ChocDino.UIFX.Demos.csproj", "msbuildProject": "ChocDino.UIFX.Demos.csproj"}, "ChocDino.UIFX.Editor/1.0.0": {"type": "project", "path": "ChocDino.UIFX.Editor.csproj", "msbuildProject": "ChocDino.UIFX.Editor.csproj"}, "ChocDino.UIFX.TMP/1.0.0": {"type": "project", "path": "ChocDino.UIFX.TMP.csproj", "msbuildProject": "ChocDino.UIFX.TMP.csproj"}, "ChocDino.UIFX.TMP.Editor/1.0.0": {"type": "project", "path": "ChocDino.UIFX.TMP.Editor.csproj", "msbuildProject": "ChocDino.UIFX.TMP.Editor.csproj"}, "ChocDino.UIFX.UITK/1.0.0": {"type": "project", "path": "ChocDino.UIFX.UITK.csproj", "msbuildProject": "ChocDino.UIFX.UITK.csproj"}, "ChocDino.UIFX.UITK.Editor/1.0.0": {"type": "project", "path": "ChocDino.UIFX.UITK.Editor.csproj", "msbuildProject": "ChocDino.UIFX.UITK.Editor.csproj"}, "DOTween.Modules/1.0.0": {"type": "project", "path": "DOTween.Modules.csproj", "msbuildProject": "DOTween.Modules.csproj"}, "DOTweenPro.Scripts/1.0.0": {"type": "project", "path": "DOTweenPro.Scripts.csproj", "msbuildProject": "DOTweenPro.Scripts.csproj"}, "OSA.Core/1.0.0": {"type": "project", "path": "OSA.Core.csproj", "msbuildProject": "OSA.Core.csproj"}, "OSA.Core.Editor/1.0.0": {"type": "project", "path": "OSA.Core.Editor.csproj", "msbuildProject": "OSA.Core.Editor.csproj"}, "OSA.Demos/1.0.0": {"type": "project", "path": "OSA.Demos.csproj", "msbuildProject": "OSA.Demos.csproj"}, "OSA.Utilities/1.0.0": {"type": "project", "path": "OSA.Utilities.csproj", "msbuildProject": "OSA.Utilities.csproj"}, "OSA.Utilities.Editor/1.0.0": {"type": "project", "path": "OSA.Utilities.Editor.csproj", "msbuildProject": "OSA.Utilities.Editor.csproj"}, "UniTask/1.0.0": {"type": "project", "path": "UniTask.csproj", "msbuildProject": "UniTask.csproj"}, "UniTask.Addressables/1.0.0": {"type": "project", "path": "UniTask.Addressables.csproj", "msbuildProject": "UniTask.Addressables.csproj"}, "UniTask.DOTween/1.0.0": {"type": "project", "path": "UniTask.DOTween.csproj", "msbuildProject": "UniTask.DOTween.csproj"}, "UniTask.Linq/1.0.0": {"type": "project", "path": "UniTask.Linq.csproj", "msbuildProject": "UniTask.Linq.csproj"}, "UniTask.TextMeshPro/1.0.0": {"type": "project", "path": "UniTask.TextMeshPro.csproj", "msbuildProject": "UniTask.TextMeshPro.csproj"}, "UnityUIExtensions/1.0.0": {"type": "project", "path": "UnityUIExtensions.csproj", "msbuildProject": "UnityUIExtensions.csproj"}, "UnityUIExtensions.editor/1.0.0": {"type": "project", "path": "UnityUIExtensions.editor.csproj", "msbuildProject": "UnityUIExtensions.editor.csproj"}, "UnityUIExtensions.examples/1.0.0": {"type": "project", "path": "UnityUIExtensions.examples.csproj", "msbuildProject": "UnityUIExtensions.examples.csproj"}, "UnityWebSocket.Editor/1.0.0": {"type": "project", "path": "UnityWebSocket.Editor.csproj", "msbuildProject": "UnityWebSocket.Editor.csproj"}, "UnityWebSocket.Runtime/1.0.0": {"type": "project", "path": "UnityWebSocket.Runtime.csproj", "msbuildProject": "UnityWebSocket.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["ChocDino.UIFX >= 1.0.0", "ChocDino.UIFX.Demos >= 1.0.0", "ChocDino.UIFX.Editor >= 1.0.0", "ChocDino.UIFX.TMP >= 1.0.0", "ChocDino.UIFX.TMP.Editor >= 1.0.0", "ChocDino.UIFX.UITK >= 1.0.0", "ChocDino.UIFX.UITK.Editor >= 1.0.0", "DOTween.Modules >= 1.0.0", "DOTweenPro.Scripts >= 1.0.0", "OSA.Core >= 1.0.0", "OSA.Core.Editor >= 1.0.0", "OSA.Demos >= 1.0.0", "OSA.Utilities >= 1.0.0", "OSA.Utilities.Editor >= 1.0.0", "UniTask >= 1.0.0", "UniTask.Addressables >= 1.0.0", "UniTask.DOTween >= 1.0.0", "UniTask.Linq >= 1.0.0", "UniTask.TextMeshPro >= 1.0.0", "UnityUIExtensions >= 1.0.0", "UnityUIExtensions.editor >= 1.0.0", "UnityUIExtensions.examples >= 1.0.0", "UnityWebSocket.Editor >= 1.0.0", "UnityWebSocket.Runtime >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\unity\\HeroCastleRemake-Unity6.1\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.Demos.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.Demos.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.Editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.Editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.TMP.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.TMP.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.TMP.Editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.TMP.Editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.UITK.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.UITK.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.UITK.Editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\ChocDino.UIFX.UITK.Editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\DOTween.Modules.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\DOTween.Modules.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\DOTweenPro.Scripts.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\DOTweenPro.Scripts.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Core.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Core.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Core.Editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Core.Editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Demos.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Demos.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Utilities.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Utilities.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Utilities.Editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\OSA.Utilities.Editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.Addressables.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.Addressables.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.DOTween.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.DOTween.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.Linq.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.Linq.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.TextMeshPro.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityUIExtensions.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityUIExtensions.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityUIExtensions.editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityUIExtensions.editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityUIExtensions.examples.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityUIExtensions.examples.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Editor.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Editor.csproj"}, "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.202\\RuntimeIdentifierGraph.json"}}}}