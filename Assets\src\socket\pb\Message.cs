// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Assets/src/socket/protobuf/message.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Pb {

  /// <summary>Holder for reflection information generated from Assets/src/socket/protobuf/message.proto</summary>
  public static partial class MessageReflection {

    #region Descriptor
    /// <summary>File descriptor for Assets/src/socket/protobuf/message.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MessageReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CihBc3NldHMvc3JjL3NvY2tldC9wcm90b2J1Zi9tZXNzYWdlLnByb3RvEgJw",
            "YhosQXNzZXRzL3NyYy9zb2NrZXQvcHJvdG9idWYvbG9naW4vbG9naW4ucHJv",
            "dG8aOkFzc2V0cy9zcmMvc29ja2V0L3Byb3RvYnVmL3Byb3h5L09iamVjdFBy",
            "b3BlcnR5UHJveHkucHJvdG8aMUFzc2V0cy9zcmMvc29ja2V0L3Byb3RvYnVm",
            "L3Byb3h5L0dvb2RzUHJveHkucHJvdG8aMEFzc2V0cy9zcmMvc29ja2V0L3By",
            "b3RvYnVmL3Byb3h5L0NoYXRQcm94eS5wcm90bxoxQXNzZXRzL3NyYy9zb2Nr",
            "ZXQvcHJvdG9idWYvcHJveHkvU2tpbGxQcm94eS5wcm90bxo1QXNzZXRzL3Ny",
            "Yy9zb2NrZXQvcHJvdG9idWYvcHJveHkvRXF1aXBtZW50UHJveHkucHJvdG8a",
            "NkFzc2V0cy9zcmMvc29ja2V0L3Byb3RvYnVmL3NlcnZlci9TZXJ2ZXJNZWRp",
            "YXRvci5wcm90byKhCAoHTWVzc2FnZRIOCgZhY3Rpb24YASABKAUSKQoLd2Vs",
            "Y29tZV9tc2cYAiABKAsyEi5wYi5XZWxjb21lTWVzc2FnZUgAEiUKCWxvZ2lu",
            "X3JlcRgDIAEoCzIQLnBiLkxvZ2luUmVxdWVzdEgAEiYKCWxvZ2luX3JlcxgE",
            "IAEoCzIRLnBiLkxvZ2luUmVzcG9uc2VIABI3ChJvYmplY3RQcm9wZXJ0eV9y",
            "ZXEYBSABKAsyGS5wYi5PYmplY3RQcm9wZXJ0eVJlcXVlc3RIABI4ChJvYmpl",
            "Y3RQcm9wZXJ0eV9yZXMYBiABKAsyGi5wYi5PYmplY3RQcm9wZXJ0eVJlc3Bv",
            "bnNlSAASMgoPcGxheWVyR29vZHNfcmVzGAcgASgLMhcucGIuUGxheWVyR29v",
            "ZHNSZXNwb25zZUgAEjUKEWNsYXNzUHJvcGVydHlfcmVxGAggASgLMhgucGIu",
            "Q2xhc3NQcm9wZXJ0eVJlcXVlc3RIABItCg1jaGF0V29ybGRfcmVxGAkgASgL",
            "MhQucGIuQ2hhdFdvcmxkUmVxdWVzdEgAEi4KDWNoYXRXb3JsZF9yZXMYCiAB",
            "KAsyFS5wYi5DaGF0V29ybGRSZXNwb25zZUgAEjoKE2NoYXRXb3JsZFJlc3Vs",
            "dF9yZXMYCyABKAsyGy5wYi5DaGF0V29ybGRSZXN1bHRSZXNwb25zZUgAEjoK",
            "E1JBQ0VfQlVJTERfTElTVF9SZXEYDCABKAsyGy5wYi5SQUNFX0JVSUxEX0xJ",
            "U1RfUmVxdWVzdEgAEkkKGlJBQ0VfQlVJTERfTElTVF9SRVNVTFRfUmVzGA0g",
            "ASgLMiMucGIuUkFDRV9CVUlMRF9MSVNUX1JFU1VMVF9SZXNwb25zZUgAEjYK",
            "EGhlcm9BZGRTa2lsbF9yZXEYDiABKAsyGi5wYi5IRVJPX0FERF9TS0lMTF9S",
            "ZXF1ZXN0SAASNwoQaGVyb0FkZFNraWxsX3JlcxgPIAEoCzIbLnBiLkhFUk9f",
            "QUREX1NLSUxMX1Jlc3BvbnNlSAASNwoRaGVyb0VxdWlwbWVudF9yZXEYECAB",
            "KAsyGi5wYi5IRVJPX0VRVUlQTUVOVF9SZXF1ZXN0SAASOAoRaGVyb0VxdWlw",
            "bWVudF9yZXMYESABKAsyGy5wYi5IRVJPX0VRVUlQTUVOVF9SZXNwb25zZUgA",
            "EjkKE3BsYXllckVxdWlwbWVudF9yZXEYEiABKAsyGi5wYi5QbGF5ZXJFcXVp",
            "cG1lbnRSZXF1ZXN0SAASOgoTcGxheWVyRXF1aXBtZW50X3JlcxgTIAEoCzIb",
            "LnBiLlBsYXllckVxdWlwbWVudFJlc3BvbnNlSAASKQoKaGVyb0pOX3JlcxgU",
            "IAEoCzITLnBiLkhFUk9KTl9SZXNwb25zZUgAQgYKBGRhdGEiIQoOV2VsY29t",
            "ZU1lc3NhZ2USDwoHY29udGVudBgBIAEoCWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Pb.LoginReflection.Descriptor, global::Pb.ObjectPropertyProxyReflection.Descriptor, global::Pb.GoodsProxyReflection.Descriptor, global::Pb.ChatProxyReflection.Descriptor, global::Pb.SkillProxyReflection.Descriptor, global::Pb.EquipmentProxyReflection.Descriptor, global::Pb.ServerMediatorReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Pb.Message), global::Pb.Message.Parser, new[]{ "Action", "WelcomeMsg", "LoginReq", "LoginRes", "ObjectPropertyReq", "ObjectPropertyRes", "PlayerGoodsRes", "ClassPropertyReq", "ChatWorldReq", "ChatWorldRes", "ChatWorldResultRes", "RACEBUILDLISTReq", "RACEBUILDLISTRESULTRes", "HeroAddSkillReq", "HeroAddSkillRes", "HeroEquipmentReq", "HeroEquipmentRes", "PlayerEquipmentReq", "PlayerEquipmentRes", "HeroJNRes" }, new[]{ "Data" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Pb.WelcomeMessage), global::Pb.WelcomeMessage.Parser, new[]{ "Content" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Message : pb::IMessage<Message>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Message> _parser = new pb::MessageParser<Message>(() => new Message());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Message> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Pb.MessageReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Message() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Message(Message other) : this() {
      action_ = other.action_;
      switch (other.DataCase) {
        case DataOneofCase.WelcomeMsg:
          WelcomeMsg = other.WelcomeMsg.Clone();
          break;
        case DataOneofCase.LoginReq:
          LoginReq = other.LoginReq.Clone();
          break;
        case DataOneofCase.LoginRes:
          LoginRes = other.LoginRes.Clone();
          break;
        case DataOneofCase.ObjectPropertyReq:
          ObjectPropertyReq = other.ObjectPropertyReq.Clone();
          break;
        case DataOneofCase.ObjectPropertyRes:
          ObjectPropertyRes = other.ObjectPropertyRes.Clone();
          break;
        case DataOneofCase.PlayerGoodsRes:
          PlayerGoodsRes = other.PlayerGoodsRes.Clone();
          break;
        case DataOneofCase.ClassPropertyReq:
          ClassPropertyReq = other.ClassPropertyReq.Clone();
          break;
        case DataOneofCase.ChatWorldReq:
          ChatWorldReq = other.ChatWorldReq.Clone();
          break;
        case DataOneofCase.ChatWorldRes:
          ChatWorldRes = other.ChatWorldRes.Clone();
          break;
        case DataOneofCase.ChatWorldResultRes:
          ChatWorldResultRes = other.ChatWorldResultRes.Clone();
          break;
        case DataOneofCase.RACEBUILDLISTReq:
          RACEBUILDLISTReq = other.RACEBUILDLISTReq.Clone();
          break;
        case DataOneofCase.RACEBUILDLISTRESULTRes:
          RACEBUILDLISTRESULTRes = other.RACEBUILDLISTRESULTRes.Clone();
          break;
        case DataOneofCase.HeroAddSkillReq:
          HeroAddSkillReq = other.HeroAddSkillReq.Clone();
          break;
        case DataOneofCase.HeroAddSkillRes:
          HeroAddSkillRes = other.HeroAddSkillRes.Clone();
          break;
        case DataOneofCase.HeroEquipmentReq:
          HeroEquipmentReq = other.HeroEquipmentReq.Clone();
          break;
        case DataOneofCase.HeroEquipmentRes:
          HeroEquipmentRes = other.HeroEquipmentRes.Clone();
          break;
        case DataOneofCase.PlayerEquipmentReq:
          PlayerEquipmentReq = other.PlayerEquipmentReq.Clone();
          break;
        case DataOneofCase.PlayerEquipmentRes:
          PlayerEquipmentRes = other.PlayerEquipmentRes.Clone();
          break;
        case DataOneofCase.HeroJNRes:
          HeroJNRes = other.HeroJNRes.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Message Clone() {
      return new Message(this);
    }

    /// <summary>Field number for the "action" field.</summary>
    public const int ActionFieldNumber = 1;
    private int action_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Action {
      get { return action_; }
      set {
        action_ = value;
      }
    }

    /// <summary>Field number for the "welcome_msg" field.</summary>
    public const int WelcomeMsgFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.WelcomeMessage WelcomeMsg {
      get { return dataCase_ == DataOneofCase.WelcomeMsg ? (global::Pb.WelcomeMessage) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.WelcomeMsg;
      }
    }

    /// <summary>Field number for the "login_req" field.</summary>
    public const int LoginReqFieldNumber = 3;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.LoginRequest LoginReq {
      get { return dataCase_ == DataOneofCase.LoginReq ? (global::Pb.LoginRequest) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.LoginReq;
      }
    }

    /// <summary>Field number for the "login_res" field.</summary>
    public const int LoginResFieldNumber = 4;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.LoginResponse LoginRes {
      get { return dataCase_ == DataOneofCase.LoginRes ? (global::Pb.LoginResponse) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.LoginRes;
      }
    }

    /// <summary>Field number for the "objectProperty_req" field.</summary>
    public const int ObjectPropertyReqFieldNumber = 5;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.ObjectPropertyRequest ObjectPropertyReq {
      get { return dataCase_ == DataOneofCase.ObjectPropertyReq ? (global::Pb.ObjectPropertyRequest) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.ObjectPropertyReq;
      }
    }

    /// <summary>Field number for the "objectProperty_res" field.</summary>
    public const int ObjectPropertyResFieldNumber = 6;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.ObjectPropertyResponse ObjectPropertyRes {
      get { return dataCase_ == DataOneofCase.ObjectPropertyRes ? (global::Pb.ObjectPropertyResponse) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.ObjectPropertyRes;
      }
    }

    /// <summary>Field number for the "playerGoods_res" field.</summary>
    public const int PlayerGoodsResFieldNumber = 7;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.PlayerGoodsResponse PlayerGoodsRes {
      get { return dataCase_ == DataOneofCase.PlayerGoodsRes ? (global::Pb.PlayerGoodsResponse) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.PlayerGoodsRes;
      }
    }

    /// <summary>Field number for the "classProperty_req" field.</summary>
    public const int ClassPropertyReqFieldNumber = 8;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.ClassPropertyRequest ClassPropertyReq {
      get { return dataCase_ == DataOneofCase.ClassPropertyReq ? (global::Pb.ClassPropertyRequest) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.ClassPropertyReq;
      }
    }

    /// <summary>Field number for the "chatWorld_req" field.</summary>
    public const int ChatWorldReqFieldNumber = 9;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.ChatWorldRequest ChatWorldReq {
      get { return dataCase_ == DataOneofCase.ChatWorldReq ? (global::Pb.ChatWorldRequest) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.ChatWorldReq;
      }
    }

    /// <summary>Field number for the "chatWorld_res" field.</summary>
    public const int ChatWorldResFieldNumber = 10;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.ChatWorldResponse ChatWorldRes {
      get { return dataCase_ == DataOneofCase.ChatWorldRes ? (global::Pb.ChatWorldResponse) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.ChatWorldRes;
      }
    }

    /// <summary>Field number for the "chatWorldResult_res" field.</summary>
    public const int ChatWorldResultResFieldNumber = 11;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.ChatWorldResultResponse ChatWorldResultRes {
      get { return dataCase_ == DataOneofCase.ChatWorldResultRes ? (global::Pb.ChatWorldResultResponse) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.ChatWorldResultRes;
      }
    }

    /// <summary>Field number for the "RACE_BUILD_LIST_Req" field.</summary>
    public const int RACEBUILDLISTReqFieldNumber = 12;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.RACE_BUILD_LIST_Request RACEBUILDLISTReq {
      get { return dataCase_ == DataOneofCase.RACEBUILDLISTReq ? (global::Pb.RACE_BUILD_LIST_Request) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.RACEBUILDLISTReq;
      }
    }

    /// <summary>Field number for the "RACE_BUILD_LIST_RESULT_Res" field.</summary>
    public const int RACEBUILDLISTRESULTResFieldNumber = 13;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.RACE_BUILD_LIST_RESULT_Response RACEBUILDLISTRESULTRes {
      get { return dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes ? (global::Pb.RACE_BUILD_LIST_RESULT_Response) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.RACEBUILDLISTRESULTRes;
      }
    }

    /// <summary>Field number for the "heroAddSkill_req" field.</summary>
    public const int HeroAddSkillReqFieldNumber = 14;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.HERO_ADD_SKILL_Request HeroAddSkillReq {
      get { return dataCase_ == DataOneofCase.HeroAddSkillReq ? (global::Pb.HERO_ADD_SKILL_Request) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.HeroAddSkillReq;
      }
    }

    /// <summary>Field number for the "heroAddSkill_res" field.</summary>
    public const int HeroAddSkillResFieldNumber = 15;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.HERO_ADD_SKILL_Response HeroAddSkillRes {
      get { return dataCase_ == DataOneofCase.HeroAddSkillRes ? (global::Pb.HERO_ADD_SKILL_Response) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.HeroAddSkillRes;
      }
    }

    /// <summary>Field number for the "heroEquipment_req" field.</summary>
    public const int HeroEquipmentReqFieldNumber = 16;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.HERO_EQUIPMENT_Request HeroEquipmentReq {
      get { return dataCase_ == DataOneofCase.HeroEquipmentReq ? (global::Pb.HERO_EQUIPMENT_Request) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.HeroEquipmentReq;
      }
    }

    /// <summary>Field number for the "heroEquipment_res" field.</summary>
    public const int HeroEquipmentResFieldNumber = 17;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.HERO_EQUIPMENT_Response HeroEquipmentRes {
      get { return dataCase_ == DataOneofCase.HeroEquipmentRes ? (global::Pb.HERO_EQUIPMENT_Response) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.HeroEquipmentRes;
      }
    }

    /// <summary>Field number for the "playerEquipment_req" field.</summary>
    public const int PlayerEquipmentReqFieldNumber = 18;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.PlayerEquipmentRequest PlayerEquipmentReq {
      get { return dataCase_ == DataOneofCase.PlayerEquipmentReq ? (global::Pb.PlayerEquipmentRequest) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.PlayerEquipmentReq;
      }
    }

    /// <summary>Field number for the "playerEquipment_res" field.</summary>
    public const int PlayerEquipmentResFieldNumber = 19;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.PlayerEquipmentResponse PlayerEquipmentRes {
      get { return dataCase_ == DataOneofCase.PlayerEquipmentRes ? (global::Pb.PlayerEquipmentResponse) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.PlayerEquipmentRes;
      }
    }

    /// <summary>Field number for the "heroJN_res" field.</summary>
    public const int HeroJNResFieldNumber = 20;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Pb.HEROJN_Response HeroJNRes {
      get { return dataCase_ == DataOneofCase.HeroJNRes ? (global::Pb.HEROJN_Response) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.HeroJNRes;
      }
    }

    private object data_;
    /// <summary>Enum of possible cases for the "data" oneof.</summary>
    public enum DataOneofCase {
      None = 0,
      WelcomeMsg = 2,
      LoginReq = 3,
      LoginRes = 4,
      ObjectPropertyReq = 5,
      ObjectPropertyRes = 6,
      PlayerGoodsRes = 7,
      ClassPropertyReq = 8,
      ChatWorldReq = 9,
      ChatWorldRes = 10,
      ChatWorldResultRes = 11,
      RACEBUILDLISTReq = 12,
      RACEBUILDLISTRESULTRes = 13,
      HeroAddSkillReq = 14,
      HeroAddSkillRes = 15,
      HeroEquipmentReq = 16,
      HeroEquipmentRes = 17,
      PlayerEquipmentReq = 18,
      PlayerEquipmentRes = 19,
      HeroJNRes = 20,
    }
    private DataOneofCase dataCase_ = DataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DataOneofCase DataCase {
      get { return dataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearData() {
      dataCase_ = DataOneofCase.None;
      data_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Message);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Message other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Action != other.Action) return false;
      if (!object.Equals(WelcomeMsg, other.WelcomeMsg)) return false;
      if (!object.Equals(LoginReq, other.LoginReq)) return false;
      if (!object.Equals(LoginRes, other.LoginRes)) return false;
      if (!object.Equals(ObjectPropertyReq, other.ObjectPropertyReq)) return false;
      if (!object.Equals(ObjectPropertyRes, other.ObjectPropertyRes)) return false;
      if (!object.Equals(PlayerGoodsRes, other.PlayerGoodsRes)) return false;
      if (!object.Equals(ClassPropertyReq, other.ClassPropertyReq)) return false;
      if (!object.Equals(ChatWorldReq, other.ChatWorldReq)) return false;
      if (!object.Equals(ChatWorldRes, other.ChatWorldRes)) return false;
      if (!object.Equals(ChatWorldResultRes, other.ChatWorldResultRes)) return false;
      if (!object.Equals(RACEBUILDLISTReq, other.RACEBUILDLISTReq)) return false;
      if (!object.Equals(RACEBUILDLISTRESULTRes, other.RACEBUILDLISTRESULTRes)) return false;
      if (!object.Equals(HeroAddSkillReq, other.HeroAddSkillReq)) return false;
      if (!object.Equals(HeroAddSkillRes, other.HeroAddSkillRes)) return false;
      if (!object.Equals(HeroEquipmentReq, other.HeroEquipmentReq)) return false;
      if (!object.Equals(HeroEquipmentRes, other.HeroEquipmentRes)) return false;
      if (!object.Equals(PlayerEquipmentReq, other.PlayerEquipmentReq)) return false;
      if (!object.Equals(PlayerEquipmentRes, other.PlayerEquipmentRes)) return false;
      if (!object.Equals(HeroJNRes, other.HeroJNRes)) return false;
      if (DataCase != other.DataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Action != 0) hash ^= Action.GetHashCode();
      if (dataCase_ == DataOneofCase.WelcomeMsg) hash ^= WelcomeMsg.GetHashCode();
      if (dataCase_ == DataOneofCase.LoginReq) hash ^= LoginReq.GetHashCode();
      if (dataCase_ == DataOneofCase.LoginRes) hash ^= LoginRes.GetHashCode();
      if (dataCase_ == DataOneofCase.ObjectPropertyReq) hash ^= ObjectPropertyReq.GetHashCode();
      if (dataCase_ == DataOneofCase.ObjectPropertyRes) hash ^= ObjectPropertyRes.GetHashCode();
      if (dataCase_ == DataOneofCase.PlayerGoodsRes) hash ^= PlayerGoodsRes.GetHashCode();
      if (dataCase_ == DataOneofCase.ClassPropertyReq) hash ^= ClassPropertyReq.GetHashCode();
      if (dataCase_ == DataOneofCase.ChatWorldReq) hash ^= ChatWorldReq.GetHashCode();
      if (dataCase_ == DataOneofCase.ChatWorldRes) hash ^= ChatWorldRes.GetHashCode();
      if (dataCase_ == DataOneofCase.ChatWorldResultRes) hash ^= ChatWorldResultRes.GetHashCode();
      if (dataCase_ == DataOneofCase.RACEBUILDLISTReq) hash ^= RACEBUILDLISTReq.GetHashCode();
      if (dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes) hash ^= RACEBUILDLISTRESULTRes.GetHashCode();
      if (dataCase_ == DataOneofCase.HeroAddSkillReq) hash ^= HeroAddSkillReq.GetHashCode();
      if (dataCase_ == DataOneofCase.HeroAddSkillRes) hash ^= HeroAddSkillRes.GetHashCode();
      if (dataCase_ == DataOneofCase.HeroEquipmentReq) hash ^= HeroEquipmentReq.GetHashCode();
      if (dataCase_ == DataOneofCase.HeroEquipmentRes) hash ^= HeroEquipmentRes.GetHashCode();
      if (dataCase_ == DataOneofCase.PlayerEquipmentReq) hash ^= PlayerEquipmentReq.GetHashCode();
      if (dataCase_ == DataOneofCase.PlayerEquipmentRes) hash ^= PlayerEquipmentRes.GetHashCode();
      if (dataCase_ == DataOneofCase.HeroJNRes) hash ^= HeroJNRes.GetHashCode();
      hash ^= (int) dataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Action != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Action);
      }
      if (dataCase_ == DataOneofCase.WelcomeMsg) {
        output.WriteRawTag(18);
        output.WriteMessage(WelcomeMsg);
      }
      if (dataCase_ == DataOneofCase.LoginReq) {
        output.WriteRawTag(26);
        output.WriteMessage(LoginReq);
      }
      if (dataCase_ == DataOneofCase.LoginRes) {
        output.WriteRawTag(34);
        output.WriteMessage(LoginRes);
      }
      if (dataCase_ == DataOneofCase.ObjectPropertyReq) {
        output.WriteRawTag(42);
        output.WriteMessage(ObjectPropertyReq);
      }
      if (dataCase_ == DataOneofCase.ObjectPropertyRes) {
        output.WriteRawTag(50);
        output.WriteMessage(ObjectPropertyRes);
      }
      if (dataCase_ == DataOneofCase.PlayerGoodsRes) {
        output.WriteRawTag(58);
        output.WriteMessage(PlayerGoodsRes);
      }
      if (dataCase_ == DataOneofCase.ClassPropertyReq) {
        output.WriteRawTag(66);
        output.WriteMessage(ClassPropertyReq);
      }
      if (dataCase_ == DataOneofCase.ChatWorldReq) {
        output.WriteRawTag(74);
        output.WriteMessage(ChatWorldReq);
      }
      if (dataCase_ == DataOneofCase.ChatWorldRes) {
        output.WriteRawTag(82);
        output.WriteMessage(ChatWorldRes);
      }
      if (dataCase_ == DataOneofCase.ChatWorldResultRes) {
        output.WriteRawTag(90);
        output.WriteMessage(ChatWorldResultRes);
      }
      if (dataCase_ == DataOneofCase.RACEBUILDLISTReq) {
        output.WriteRawTag(98);
        output.WriteMessage(RACEBUILDLISTReq);
      }
      if (dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes) {
        output.WriteRawTag(106);
        output.WriteMessage(RACEBUILDLISTRESULTRes);
      }
      if (dataCase_ == DataOneofCase.HeroAddSkillReq) {
        output.WriteRawTag(114);
        output.WriteMessage(HeroAddSkillReq);
      }
      if (dataCase_ == DataOneofCase.HeroAddSkillRes) {
        output.WriteRawTag(122);
        output.WriteMessage(HeroAddSkillRes);
      }
      if (dataCase_ == DataOneofCase.HeroEquipmentReq) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(HeroEquipmentReq);
      }
      if (dataCase_ == DataOneofCase.HeroEquipmentRes) {
        output.WriteRawTag(138, 1);
        output.WriteMessage(HeroEquipmentRes);
      }
      if (dataCase_ == DataOneofCase.PlayerEquipmentReq) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(PlayerEquipmentReq);
      }
      if (dataCase_ == DataOneofCase.PlayerEquipmentRes) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(PlayerEquipmentRes);
      }
      if (dataCase_ == DataOneofCase.HeroJNRes) {
        output.WriteRawTag(162, 1);
        output.WriteMessage(HeroJNRes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Action != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Action);
      }
      if (dataCase_ == DataOneofCase.WelcomeMsg) {
        output.WriteRawTag(18);
        output.WriteMessage(WelcomeMsg);
      }
      if (dataCase_ == DataOneofCase.LoginReq) {
        output.WriteRawTag(26);
        output.WriteMessage(LoginReq);
      }
      if (dataCase_ == DataOneofCase.LoginRes) {
        output.WriteRawTag(34);
        output.WriteMessage(LoginRes);
      }
      if (dataCase_ == DataOneofCase.ObjectPropertyReq) {
        output.WriteRawTag(42);
        output.WriteMessage(ObjectPropertyReq);
      }
      if (dataCase_ == DataOneofCase.ObjectPropertyRes) {
        output.WriteRawTag(50);
        output.WriteMessage(ObjectPropertyRes);
      }
      if (dataCase_ == DataOneofCase.PlayerGoodsRes) {
        output.WriteRawTag(58);
        output.WriteMessage(PlayerGoodsRes);
      }
      if (dataCase_ == DataOneofCase.ClassPropertyReq) {
        output.WriteRawTag(66);
        output.WriteMessage(ClassPropertyReq);
      }
      if (dataCase_ == DataOneofCase.ChatWorldReq) {
        output.WriteRawTag(74);
        output.WriteMessage(ChatWorldReq);
      }
      if (dataCase_ == DataOneofCase.ChatWorldRes) {
        output.WriteRawTag(82);
        output.WriteMessage(ChatWorldRes);
      }
      if (dataCase_ == DataOneofCase.ChatWorldResultRes) {
        output.WriteRawTag(90);
        output.WriteMessage(ChatWorldResultRes);
      }
      if (dataCase_ == DataOneofCase.RACEBUILDLISTReq) {
        output.WriteRawTag(98);
        output.WriteMessage(RACEBUILDLISTReq);
      }
      if (dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes) {
        output.WriteRawTag(106);
        output.WriteMessage(RACEBUILDLISTRESULTRes);
      }
      if (dataCase_ == DataOneofCase.HeroAddSkillReq) {
        output.WriteRawTag(114);
        output.WriteMessage(HeroAddSkillReq);
      }
      if (dataCase_ == DataOneofCase.HeroAddSkillRes) {
        output.WriteRawTag(122);
        output.WriteMessage(HeroAddSkillRes);
      }
      if (dataCase_ == DataOneofCase.HeroEquipmentReq) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(HeroEquipmentReq);
      }
      if (dataCase_ == DataOneofCase.HeroEquipmentRes) {
        output.WriteRawTag(138, 1);
        output.WriteMessage(HeroEquipmentRes);
      }
      if (dataCase_ == DataOneofCase.PlayerEquipmentReq) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(PlayerEquipmentReq);
      }
      if (dataCase_ == DataOneofCase.PlayerEquipmentRes) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(PlayerEquipmentRes);
      }
      if (dataCase_ == DataOneofCase.HeroJNRes) {
        output.WriteRawTag(162, 1);
        output.WriteMessage(HeroJNRes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Action != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Action);
      }
      if (dataCase_ == DataOneofCase.WelcomeMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(WelcomeMsg);
      }
      if (dataCase_ == DataOneofCase.LoginReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(LoginReq);
      }
      if (dataCase_ == DataOneofCase.LoginRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(LoginRes);
      }
      if (dataCase_ == DataOneofCase.ObjectPropertyReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ObjectPropertyReq);
      }
      if (dataCase_ == DataOneofCase.ObjectPropertyRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ObjectPropertyRes);
      }
      if (dataCase_ == DataOneofCase.PlayerGoodsRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PlayerGoodsRes);
      }
      if (dataCase_ == DataOneofCase.ClassPropertyReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ClassPropertyReq);
      }
      if (dataCase_ == DataOneofCase.ChatWorldReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ChatWorldReq);
      }
      if (dataCase_ == DataOneofCase.ChatWorldRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ChatWorldRes);
      }
      if (dataCase_ == DataOneofCase.ChatWorldResultRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ChatWorldResultRes);
      }
      if (dataCase_ == DataOneofCase.RACEBUILDLISTReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RACEBUILDLISTReq);
      }
      if (dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RACEBUILDLISTRESULTRes);
      }
      if (dataCase_ == DataOneofCase.HeroAddSkillReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(HeroAddSkillReq);
      }
      if (dataCase_ == DataOneofCase.HeroAddSkillRes) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(HeroAddSkillRes);
      }
      if (dataCase_ == DataOneofCase.HeroEquipmentReq) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(HeroEquipmentReq);
      }
      if (dataCase_ == DataOneofCase.HeroEquipmentRes) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(HeroEquipmentRes);
      }
      if (dataCase_ == DataOneofCase.PlayerEquipmentReq) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(PlayerEquipmentReq);
      }
      if (dataCase_ == DataOneofCase.PlayerEquipmentRes) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(PlayerEquipmentRes);
      }
      if (dataCase_ == DataOneofCase.HeroJNRes) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(HeroJNRes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Message other) {
      if (other == null) {
        return;
      }
      if (other.Action != 0) {
        Action = other.Action;
      }
      switch (other.DataCase) {
        case DataOneofCase.WelcomeMsg:
          if (WelcomeMsg == null) {
            WelcomeMsg = new global::Pb.WelcomeMessage();
          }
          WelcomeMsg.MergeFrom(other.WelcomeMsg);
          break;
        case DataOneofCase.LoginReq:
          if (LoginReq == null) {
            LoginReq = new global::Pb.LoginRequest();
          }
          LoginReq.MergeFrom(other.LoginReq);
          break;
        case DataOneofCase.LoginRes:
          if (LoginRes == null) {
            LoginRes = new global::Pb.LoginResponse();
          }
          LoginRes.MergeFrom(other.LoginRes);
          break;
        case DataOneofCase.ObjectPropertyReq:
          if (ObjectPropertyReq == null) {
            ObjectPropertyReq = new global::Pb.ObjectPropertyRequest();
          }
          ObjectPropertyReq.MergeFrom(other.ObjectPropertyReq);
          break;
        case DataOneofCase.ObjectPropertyRes:
          if (ObjectPropertyRes == null) {
            ObjectPropertyRes = new global::Pb.ObjectPropertyResponse();
          }
          ObjectPropertyRes.MergeFrom(other.ObjectPropertyRes);
          break;
        case DataOneofCase.PlayerGoodsRes:
          if (PlayerGoodsRes == null) {
            PlayerGoodsRes = new global::Pb.PlayerGoodsResponse();
          }
          PlayerGoodsRes.MergeFrom(other.PlayerGoodsRes);
          break;
        case DataOneofCase.ClassPropertyReq:
          if (ClassPropertyReq == null) {
            ClassPropertyReq = new global::Pb.ClassPropertyRequest();
          }
          ClassPropertyReq.MergeFrom(other.ClassPropertyReq);
          break;
        case DataOneofCase.ChatWorldReq:
          if (ChatWorldReq == null) {
            ChatWorldReq = new global::Pb.ChatWorldRequest();
          }
          ChatWorldReq.MergeFrom(other.ChatWorldReq);
          break;
        case DataOneofCase.ChatWorldRes:
          if (ChatWorldRes == null) {
            ChatWorldRes = new global::Pb.ChatWorldResponse();
          }
          ChatWorldRes.MergeFrom(other.ChatWorldRes);
          break;
        case DataOneofCase.ChatWorldResultRes:
          if (ChatWorldResultRes == null) {
            ChatWorldResultRes = new global::Pb.ChatWorldResultResponse();
          }
          ChatWorldResultRes.MergeFrom(other.ChatWorldResultRes);
          break;
        case DataOneofCase.RACEBUILDLISTReq:
          if (RACEBUILDLISTReq == null) {
            RACEBUILDLISTReq = new global::Pb.RACE_BUILD_LIST_Request();
          }
          RACEBUILDLISTReq.MergeFrom(other.RACEBUILDLISTReq);
          break;
        case DataOneofCase.RACEBUILDLISTRESULTRes:
          if (RACEBUILDLISTRESULTRes == null) {
            RACEBUILDLISTRESULTRes = new global::Pb.RACE_BUILD_LIST_RESULT_Response();
          }
          RACEBUILDLISTRESULTRes.MergeFrom(other.RACEBUILDLISTRESULTRes);
          break;
        case DataOneofCase.HeroAddSkillReq:
          if (HeroAddSkillReq == null) {
            HeroAddSkillReq = new global::Pb.HERO_ADD_SKILL_Request();
          }
          HeroAddSkillReq.MergeFrom(other.HeroAddSkillReq);
          break;
        case DataOneofCase.HeroAddSkillRes:
          if (HeroAddSkillRes == null) {
            HeroAddSkillRes = new global::Pb.HERO_ADD_SKILL_Response();
          }
          HeroAddSkillRes.MergeFrom(other.HeroAddSkillRes);
          break;
        case DataOneofCase.HeroEquipmentReq:
          if (HeroEquipmentReq == null) {
            HeroEquipmentReq = new global::Pb.HERO_EQUIPMENT_Request();
          }
          HeroEquipmentReq.MergeFrom(other.HeroEquipmentReq);
          break;
        case DataOneofCase.HeroEquipmentRes:
          if (HeroEquipmentRes == null) {
            HeroEquipmentRes = new global::Pb.HERO_EQUIPMENT_Response();
          }
          HeroEquipmentRes.MergeFrom(other.HeroEquipmentRes);
          break;
        case DataOneofCase.PlayerEquipmentReq:
          if (PlayerEquipmentReq == null) {
            PlayerEquipmentReq = new global::Pb.PlayerEquipmentRequest();
          }
          PlayerEquipmentReq.MergeFrom(other.PlayerEquipmentReq);
          break;
        case DataOneofCase.PlayerEquipmentRes:
          if (PlayerEquipmentRes == null) {
            PlayerEquipmentRes = new global::Pb.PlayerEquipmentResponse();
          }
          PlayerEquipmentRes.MergeFrom(other.PlayerEquipmentRes);
          break;
        case DataOneofCase.HeroJNRes:
          if (HeroJNRes == null) {
            HeroJNRes = new global::Pb.HEROJN_Response();
          }
          HeroJNRes.MergeFrom(other.HeroJNRes);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Action = input.ReadInt32();
            break;
          }
          case 18: {
            global::Pb.WelcomeMessage subBuilder = new global::Pb.WelcomeMessage();
            if (dataCase_ == DataOneofCase.WelcomeMsg) {
              subBuilder.MergeFrom(WelcomeMsg);
            }
            input.ReadMessage(subBuilder);
            WelcomeMsg = subBuilder;
            break;
          }
          case 26: {
            global::Pb.LoginRequest subBuilder = new global::Pb.LoginRequest();
            if (dataCase_ == DataOneofCase.LoginReq) {
              subBuilder.MergeFrom(LoginReq);
            }
            input.ReadMessage(subBuilder);
            LoginReq = subBuilder;
            break;
          }
          case 34: {
            global::Pb.LoginResponse subBuilder = new global::Pb.LoginResponse();
            if (dataCase_ == DataOneofCase.LoginRes) {
              subBuilder.MergeFrom(LoginRes);
            }
            input.ReadMessage(subBuilder);
            LoginRes = subBuilder;
            break;
          }
          case 42: {
            global::Pb.ObjectPropertyRequest subBuilder = new global::Pb.ObjectPropertyRequest();
            if (dataCase_ == DataOneofCase.ObjectPropertyReq) {
              subBuilder.MergeFrom(ObjectPropertyReq);
            }
            input.ReadMessage(subBuilder);
            ObjectPropertyReq = subBuilder;
            break;
          }
          case 50: {
            global::Pb.ObjectPropertyResponse subBuilder = new global::Pb.ObjectPropertyResponse();
            if (dataCase_ == DataOneofCase.ObjectPropertyRes) {
              subBuilder.MergeFrom(ObjectPropertyRes);
            }
            input.ReadMessage(subBuilder);
            ObjectPropertyRes = subBuilder;
            break;
          }
          case 58: {
            global::Pb.PlayerGoodsResponse subBuilder = new global::Pb.PlayerGoodsResponse();
            if (dataCase_ == DataOneofCase.PlayerGoodsRes) {
              subBuilder.MergeFrom(PlayerGoodsRes);
            }
            input.ReadMessage(subBuilder);
            PlayerGoodsRes = subBuilder;
            break;
          }
          case 66: {
            global::Pb.ClassPropertyRequest subBuilder = new global::Pb.ClassPropertyRequest();
            if (dataCase_ == DataOneofCase.ClassPropertyReq) {
              subBuilder.MergeFrom(ClassPropertyReq);
            }
            input.ReadMessage(subBuilder);
            ClassPropertyReq = subBuilder;
            break;
          }
          case 74: {
            global::Pb.ChatWorldRequest subBuilder = new global::Pb.ChatWorldRequest();
            if (dataCase_ == DataOneofCase.ChatWorldReq) {
              subBuilder.MergeFrom(ChatWorldReq);
            }
            input.ReadMessage(subBuilder);
            ChatWorldReq = subBuilder;
            break;
          }
          case 82: {
            global::Pb.ChatWorldResponse subBuilder = new global::Pb.ChatWorldResponse();
            if (dataCase_ == DataOneofCase.ChatWorldRes) {
              subBuilder.MergeFrom(ChatWorldRes);
            }
            input.ReadMessage(subBuilder);
            ChatWorldRes = subBuilder;
            break;
          }
          case 90: {
            global::Pb.ChatWorldResultResponse subBuilder = new global::Pb.ChatWorldResultResponse();
            if (dataCase_ == DataOneofCase.ChatWorldResultRes) {
              subBuilder.MergeFrom(ChatWorldResultRes);
            }
            input.ReadMessage(subBuilder);
            ChatWorldResultRes = subBuilder;
            break;
          }
          case 98: {
            global::Pb.RACE_BUILD_LIST_Request subBuilder = new global::Pb.RACE_BUILD_LIST_Request();
            if (dataCase_ == DataOneofCase.RACEBUILDLISTReq) {
              subBuilder.MergeFrom(RACEBUILDLISTReq);
            }
            input.ReadMessage(subBuilder);
            RACEBUILDLISTReq = subBuilder;
            break;
          }
          case 106: {
            global::Pb.RACE_BUILD_LIST_RESULT_Response subBuilder = new global::Pb.RACE_BUILD_LIST_RESULT_Response();
            if (dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes) {
              subBuilder.MergeFrom(RACEBUILDLISTRESULTRes);
            }
            input.ReadMessage(subBuilder);
            RACEBUILDLISTRESULTRes = subBuilder;
            break;
          }
          case 114: {
            global::Pb.HERO_ADD_SKILL_Request subBuilder = new global::Pb.HERO_ADD_SKILL_Request();
            if (dataCase_ == DataOneofCase.HeroAddSkillReq) {
              subBuilder.MergeFrom(HeroAddSkillReq);
            }
            input.ReadMessage(subBuilder);
            HeroAddSkillReq = subBuilder;
            break;
          }
          case 122: {
            global::Pb.HERO_ADD_SKILL_Response subBuilder = new global::Pb.HERO_ADD_SKILL_Response();
            if (dataCase_ == DataOneofCase.HeroAddSkillRes) {
              subBuilder.MergeFrom(HeroAddSkillRes);
            }
            input.ReadMessage(subBuilder);
            HeroAddSkillRes = subBuilder;
            break;
          }
          case 130: {
            global::Pb.HERO_EQUIPMENT_Request subBuilder = new global::Pb.HERO_EQUIPMENT_Request();
            if (dataCase_ == DataOneofCase.HeroEquipmentReq) {
              subBuilder.MergeFrom(HeroEquipmentReq);
            }
            input.ReadMessage(subBuilder);
            HeroEquipmentReq = subBuilder;
            break;
          }
          case 138: {
            global::Pb.HERO_EQUIPMENT_Response subBuilder = new global::Pb.HERO_EQUIPMENT_Response();
            if (dataCase_ == DataOneofCase.HeroEquipmentRes) {
              subBuilder.MergeFrom(HeroEquipmentRes);
            }
            input.ReadMessage(subBuilder);
            HeroEquipmentRes = subBuilder;
            break;
          }
          case 146: {
            global::Pb.PlayerEquipmentRequest subBuilder = new global::Pb.PlayerEquipmentRequest();
            if (dataCase_ == DataOneofCase.PlayerEquipmentReq) {
              subBuilder.MergeFrom(PlayerEquipmentReq);
            }
            input.ReadMessage(subBuilder);
            PlayerEquipmentReq = subBuilder;
            break;
          }
          case 154: {
            global::Pb.PlayerEquipmentResponse subBuilder = new global::Pb.PlayerEquipmentResponse();
            if (dataCase_ == DataOneofCase.PlayerEquipmentRes) {
              subBuilder.MergeFrom(PlayerEquipmentRes);
            }
            input.ReadMessage(subBuilder);
            PlayerEquipmentRes = subBuilder;
            break;
          }
          case 162: {
            global::Pb.HEROJN_Response subBuilder = new global::Pb.HEROJN_Response();
            if (dataCase_ == DataOneofCase.HeroJNRes) {
              subBuilder.MergeFrom(HeroJNRes);
            }
            input.ReadMessage(subBuilder);
            HeroJNRes = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Action = input.ReadInt32();
            break;
          }
          case 18: {
            global::Pb.WelcomeMessage subBuilder = new global::Pb.WelcomeMessage();
            if (dataCase_ == DataOneofCase.WelcomeMsg) {
              subBuilder.MergeFrom(WelcomeMsg);
            }
            input.ReadMessage(subBuilder);
            WelcomeMsg = subBuilder;
            break;
          }
          case 26: {
            global::Pb.LoginRequest subBuilder = new global::Pb.LoginRequest();
            if (dataCase_ == DataOneofCase.LoginReq) {
              subBuilder.MergeFrom(LoginReq);
            }
            input.ReadMessage(subBuilder);
            LoginReq = subBuilder;
            break;
          }
          case 34: {
            global::Pb.LoginResponse subBuilder = new global::Pb.LoginResponse();
            if (dataCase_ == DataOneofCase.LoginRes) {
              subBuilder.MergeFrom(LoginRes);
            }
            input.ReadMessage(subBuilder);
            LoginRes = subBuilder;
            break;
          }
          case 42: {
            global::Pb.ObjectPropertyRequest subBuilder = new global::Pb.ObjectPropertyRequest();
            if (dataCase_ == DataOneofCase.ObjectPropertyReq) {
              subBuilder.MergeFrom(ObjectPropertyReq);
            }
            input.ReadMessage(subBuilder);
            ObjectPropertyReq = subBuilder;
            break;
          }
          case 50: {
            global::Pb.ObjectPropertyResponse subBuilder = new global::Pb.ObjectPropertyResponse();
            if (dataCase_ == DataOneofCase.ObjectPropertyRes) {
              subBuilder.MergeFrom(ObjectPropertyRes);
            }
            input.ReadMessage(subBuilder);
            ObjectPropertyRes = subBuilder;
            break;
          }
          case 58: {
            global::Pb.PlayerGoodsResponse subBuilder = new global::Pb.PlayerGoodsResponse();
            if (dataCase_ == DataOneofCase.PlayerGoodsRes) {
              subBuilder.MergeFrom(PlayerGoodsRes);
            }
            input.ReadMessage(subBuilder);
            PlayerGoodsRes = subBuilder;
            break;
          }
          case 66: {
            global::Pb.ClassPropertyRequest subBuilder = new global::Pb.ClassPropertyRequest();
            if (dataCase_ == DataOneofCase.ClassPropertyReq) {
              subBuilder.MergeFrom(ClassPropertyReq);
            }
            input.ReadMessage(subBuilder);
            ClassPropertyReq = subBuilder;
            break;
          }
          case 74: {
            global::Pb.ChatWorldRequest subBuilder = new global::Pb.ChatWorldRequest();
            if (dataCase_ == DataOneofCase.ChatWorldReq) {
              subBuilder.MergeFrom(ChatWorldReq);
            }
            input.ReadMessage(subBuilder);
            ChatWorldReq = subBuilder;
            break;
          }
          case 82: {
            global::Pb.ChatWorldResponse subBuilder = new global::Pb.ChatWorldResponse();
            if (dataCase_ == DataOneofCase.ChatWorldRes) {
              subBuilder.MergeFrom(ChatWorldRes);
            }
            input.ReadMessage(subBuilder);
            ChatWorldRes = subBuilder;
            break;
          }
          case 90: {
            global::Pb.ChatWorldResultResponse subBuilder = new global::Pb.ChatWorldResultResponse();
            if (dataCase_ == DataOneofCase.ChatWorldResultRes) {
              subBuilder.MergeFrom(ChatWorldResultRes);
            }
            input.ReadMessage(subBuilder);
            ChatWorldResultRes = subBuilder;
            break;
          }
          case 98: {
            global::Pb.RACE_BUILD_LIST_Request subBuilder = new global::Pb.RACE_BUILD_LIST_Request();
            if (dataCase_ == DataOneofCase.RACEBUILDLISTReq) {
              subBuilder.MergeFrom(RACEBUILDLISTReq);
            }
            input.ReadMessage(subBuilder);
            RACEBUILDLISTReq = subBuilder;
            break;
          }
          case 106: {
            global::Pb.RACE_BUILD_LIST_RESULT_Response subBuilder = new global::Pb.RACE_BUILD_LIST_RESULT_Response();
            if (dataCase_ == DataOneofCase.RACEBUILDLISTRESULTRes) {
              subBuilder.MergeFrom(RACEBUILDLISTRESULTRes);
            }
            input.ReadMessage(subBuilder);
            RACEBUILDLISTRESULTRes = subBuilder;
            break;
          }
          case 114: {
            global::Pb.HERO_ADD_SKILL_Request subBuilder = new global::Pb.HERO_ADD_SKILL_Request();
            if (dataCase_ == DataOneofCase.HeroAddSkillReq) {
              subBuilder.MergeFrom(HeroAddSkillReq);
            }
            input.ReadMessage(subBuilder);
            HeroAddSkillReq = subBuilder;
            break;
          }
          case 122: {
            global::Pb.HERO_ADD_SKILL_Response subBuilder = new global::Pb.HERO_ADD_SKILL_Response();
            if (dataCase_ == DataOneofCase.HeroAddSkillRes) {
              subBuilder.MergeFrom(HeroAddSkillRes);
            }
            input.ReadMessage(subBuilder);
            HeroAddSkillRes = subBuilder;
            break;
          }
          case 130: {
            global::Pb.HERO_EQUIPMENT_Request subBuilder = new global::Pb.HERO_EQUIPMENT_Request();
            if (dataCase_ == DataOneofCase.HeroEquipmentReq) {
              subBuilder.MergeFrom(HeroEquipmentReq);
            }
            input.ReadMessage(subBuilder);
            HeroEquipmentReq = subBuilder;
            break;
          }
          case 138: {
            global::Pb.HERO_EQUIPMENT_Response subBuilder = new global::Pb.HERO_EQUIPMENT_Response();
            if (dataCase_ == DataOneofCase.HeroEquipmentRes) {
              subBuilder.MergeFrom(HeroEquipmentRes);
            }
            input.ReadMessage(subBuilder);
            HeroEquipmentRes = subBuilder;
            break;
          }
          case 146: {
            global::Pb.PlayerEquipmentRequest subBuilder = new global::Pb.PlayerEquipmentRequest();
            if (dataCase_ == DataOneofCase.PlayerEquipmentReq) {
              subBuilder.MergeFrom(PlayerEquipmentReq);
            }
            input.ReadMessage(subBuilder);
            PlayerEquipmentReq = subBuilder;
            break;
          }
          case 154: {
            global::Pb.PlayerEquipmentResponse subBuilder = new global::Pb.PlayerEquipmentResponse();
            if (dataCase_ == DataOneofCase.PlayerEquipmentRes) {
              subBuilder.MergeFrom(PlayerEquipmentRes);
            }
            input.ReadMessage(subBuilder);
            PlayerEquipmentRes = subBuilder;
            break;
          }
          case 162: {
            global::Pb.HEROJN_Response subBuilder = new global::Pb.HEROJN_Response();
            if (dataCase_ == DataOneofCase.HeroJNRes) {
              subBuilder.MergeFrom(HeroJNRes);
            }
            input.ReadMessage(subBuilder);
            HeroJNRes = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WelcomeMessage : pb::IMessage<WelcomeMessage>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WelcomeMessage> _parser = new pb::MessageParser<WelcomeMessage>(() => new WelcomeMessage());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WelcomeMessage> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Pb.MessageReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WelcomeMessage() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WelcomeMessage(WelcomeMessage other) : this() {
      content_ = other.content_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WelcomeMessage Clone() {
      return new WelcomeMessage(this);
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int ContentFieldNumber = 1;
    private string content_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WelcomeMessage);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WelcomeMessage other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Content != other.Content) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Content.Length != 0) hash ^= Content.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Content.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Content);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Content.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Content);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Content);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WelcomeMessage other) {
      if (other == null) {
        return;
      }
      if (other.Content.Length != 0) {
        Content = other.Content;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Content = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Content = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
