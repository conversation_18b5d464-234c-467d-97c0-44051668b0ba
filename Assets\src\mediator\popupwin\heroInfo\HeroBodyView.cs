
using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;

public class HeroBodyView : ScaleSprite
{
   public const string BG = "hero_bg_";

   private HeroVO hero_vo;

   private int hero_id = 0;

   private int hero_race = 0;

   public CommonUILoader bg_loader;

   public CommonUILoader heroBody_img_loader;

   public SimpleMovieClip heroBody_swf_loader;
   //   
   //   private var heroBody_swf_Request:URLRequest;

   //   private var swf:MovieClip;

   //   private var swf_Child:MovieClip;

   //   private var mask_mc;

   public SmallButton btn;

   private Coroutine timer;

   public CommonImageLoader hero_kuang;

   protected override void Awake()
   {
      base.Awake();

   }

   private void Start()
   {
      btn.visible = false;
      btn.label = LanguageManager.getPopupWin("FEATURE_WIN_FEATURE");
   }

   public async UniTaskVoid setHeroVO(HeroVO _vo)
   {
      if (_vo != null)
      {
         if (hero_vo != null)
         {
            if (_vo.targetId == hero_id && _vo.race == hero_vo.race && _vo.image == hero_vo.image && _vo.sex == hero_vo.sex)
            {
               return;
            }
            onRemove();
            // hero_vo = new();
            hero_vo = _vo;
            hero_id = hero_vo.targetId;
            hero_race = hero_vo.race;
         }
         else
         {
            // hero_vo = new();
            hero_vo = _vo;
            hero_id = hero_vo.targetId;
            hero_race = hero_vo.race;
         }
      }
      bg_loader.source = await ResPathManager.getMenuIcon(BG + hero_vo.race);
      // addChildAt(bg_loader, numChildren - 2);
      // bg_loader.scaleContent = false;
      // heroBody_img_loader = new CommonUILoader();
      // heroBody_img_loader.source = ResPathManager.getHeroHeadImage(hero_vo.image, "L");
      // heroBody_img_loader.scaleContent = false;
      // addChildAt(heroBody_img_loader, numChildren - 2);
      // timer = new Timer(2000, 1);
      // timer.addEventListener(TimerEvent.TIMER_COMPLETE, timerComplete);
      // timer.start();
      List<AnimeFrameInfo> animeFrames = await AnimeFrameManager.LoadTempHeroBodyFrameData(hero_vo.image, "B");
      Debug.Log("LoadTempHeroBodyFrameData: " + animeFrames.Count);
      heroBody_swf_loader.SetAnimeFrameData(animeFrames);
      // 等待两秒
      await UniTask.Delay(2000);
      heroBody_swf_loader.PlayAnimation();

   }


   IEnumerator timerComplete()
   {
      yield return new WaitForSeconds(2f);
      heroBody_swf_loader.PlayAnimation();
   }

   private void OnDestroy()
   {
      onRemove();
      Debug.Log("HeroBodyView OnDestroy");
   }

   public void onRemove()
   {
      ToolTipManager.getInstance().unRegTarget(hero_kuang.gameObject);
      // if (Boolean(bg_loader))
      // {
      //    if (contains(bg_loader))
      //    {
      //       bg_loader.source = null;
      //       removeChild(bg_loader);
      //    }
      //    bg_loader.unload();
      //    bg_loader = null;
      // }
      bg_loader.Unload();
      // if (Boolean(heroBody_img_loader))
      // {
      //    if (contains(heroBody_img_loader))
      //    {
      //       heroBody_img_loader.source = null;
      //       removeChild(heroBody_img_loader);
      //    }
      //    heroBody_img_loader.unload();
      //    heroBody_img_loader = null;
      // }
      // if (Boolean(heroBody_swf_loader))
      // {
      //    heroBody_swf_loader.contentLoaderInfo.removeEventListener(Event.COMPLETE, showSwfHandler);
      //    heroBody_swf_loader = null;
      //    heroBody_swf_Request = null;
      // }
      // heroBody_swf_loader.StopAnimation();
      heroBody_swf_loader.Unload();
      // if (Boolean(swf))
      // {
      //    if (contains(swf))
      //    {
      //       removeChild(swf);
      //    }
      //    swf = null;
      //    swf_Child = null;
      //    heroBody_img_loader = null;
      //    heroBody_swf_Request = null;
      // }
      // if (Boolean(mask_mc))
      // {
      //    if (contains(mask_mc))
      //    {
      //       removeChild(mask_mc);
      //    }
      //    mask_mc = null;
      // }
      // if (Boolean(timer))
      // {
      //    timer.removeEventListener(TimerEvent.TIMER_COMPLETE, randomPlayHandler);
      //    timer.stop();
      //    timer = null;
      // }
      // StopCoroutine(timer);
      AnimeFrameManager.UnloadSpriteAtlas(ResPathManager.getHeroBodyAtlasPath(hero_vo.image));
      hero_vo = null;
   }


   public void showBtn(bool _bool, string tips)
   {
      btn.visible = _bool;
      if (!_bool)
      {
         ToolTipManager.getInstance().regTarget(hero_kuang.gameObject, tips);
      }
   }
}