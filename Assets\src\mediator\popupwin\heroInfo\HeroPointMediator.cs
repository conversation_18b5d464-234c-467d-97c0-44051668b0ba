using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;

public class HeroPointMediator
{
    public const string NAME = "HeroPointMediator";
    private PopupWinFrame win;

    private ObjectPropertyProxy objectPropertyProxy;

    private int heroID;

    private HeroVO heroData;

    private int type;

    private ScreenProxy screenProxy;

    private int service_Attack;

    private int service_Defense;

    private int service_Speed;

    private int service_Kownledge;

    private int serviece_armyPower;

    private int serviece_level;

    private int equip_attack;

    private int equip_defence;

    private int equip_speed;

    private int equip_kownledge;

    private int client_Attack;

    private int client_Defense;

    private int client_Speed;

    private int client_Kownledge;

    private int client_FreePoints;


    public HeroPointMediator(PopupWinFrame viewObject)
    {
        win = viewObject;
        listNotificationInterests();
        objectPropertyProxy = ProxyManager.Instance.objectPropertyProxy;
        initialize();
        screenProxy = ProxyManager.Instance.screenProxy;
        ToolTipManager.getInstance().regTarget(commonCheckBox1.gameObject, LanguageManager.getPopupWin("LABEL_SCREEN_TIP"));
        ToolTipManager.getInstance().setToolTip(commonCheckBox1.gameObject, LanguageManager.getPopupWin("LABEL_SCREEN_TIP"));
    }



    private void initialize()
    {
        nameL.editable = false;
        raceL.editable = false;
        levelL.editable = false;
        expL.editable = false;
        pointL.editable = false;
        armyPower.editable = false;
        corpsText.editable = false;
        exploitText.editable = false;
        exploitText2.editable = false;
        ToolTipManager.getInstance().regTarget(corpsText.gameObject, LanguageManager.getPopupWin("LABEL_HEROLOOK_RANKTIP"));
        ToolTipManager.getInstance().regTarget(exploitText.gameObject, LanguageManager.getPopupWin("LABEL_HEROLOOK_EXPLOITTIP"));
        // attackL.addEventListener(MouseEvent.CLICK, changePointHandler);
        // attackL.addEventListener(Event.CHANGE, changePointHandler);
        // defenceL.addEventListener(MouseEvent.CLICK, changePointHandler);
        // defenceL.addEventListener(Event.CHANGE, changePointHandler);
        // speedL.addEventListener(MouseEvent.CLICK, changePointHandler);
        // speedL.addEventListener(Event.CHANGE, changePointHandler);
        // kownledgeL.addEventListener(MouseEvent.CLICK, changePointHandler);
        // kownledgeL.addEventListener(Event.CHANGE, changePointHandler);
        // commonCheckBox1.addEventListener(Event.CHANGE, changeCheck);
        // addpointBtn.addEventListener(MouseEvent.CLICK, addPointHandler);
        // washPointsBtn.addEventListener(MouseEvent.CLICK, washpointHandler);
        // changeNameBtn.addEventListener(MouseEvent.CLICK, changeNameHandler);
        // fireOutBtn.addEventListener(MouseEvent.CLICK, affirmFireHandler);
        // disbandBtn.addEventListener(MouseEvent.CLICK, disbandHandler);
        // changeRaceBtn.addEventListener(MouseEvent.CLICK, changeRaceHandler);
        ToolTipManager.getInstance().regTarget(attackL.gameObject, HerosProTipsUtils.Attack);
        ToolTipManager.getInstance().regTarget(defenceL.gameObject, HerosProTipsUtils.Defence);
        ToolTipManager.getInstance().regTarget(kownledgeL.gameObject, HerosProTipsUtils.Kownledge);
        ToolTipManager.getInstance().regTarget(speedL.gameObject, HerosProTipsUtils.Speed);
    }

    private void getHeroID(int p_typeID)
    {
        if (p_typeID <= 0)
        {
            return;
        }
        heroID = p_typeID;
        objectPropertyProxy.getObjectPropertyCallBack(p_typeID, getHeroVo);
    }

    private void getHeroVo(object data)
    {
        if (data != null && data is List<object>)
        {
            List<object> dataArray = (List<object>)data;
            if (dataArray.Count > 0 && dataArray[0] is HeroVO p_data)
            {
                string tipstr = null;
                if (p_data.targetId != heroID)
                {
                    return;
                }
                heroData = p_data;
                service_Attack = p_data.attack;
                service_Defense = p_data.defense;
                service_Kownledge = p_data.kownledge;
                service_Speed = p_data.speed;
                nameL.htmlText = SetHerolevelUtils.setColor(p_data);
                levelL.text = p_data.rating.ToString();
                raceL.text = GameObjectUtils.getRaceName(p_data.race);
                serviece_armyPower = (int)p_data.armyMaxPower;
                Debug.Log("serviece_armyPower:" + serviece_armyPower);
                serviece_level = p_data.rating;
                List<object> rankdata = RankUtils.getInfo(p_data.exploit);
                corpsText.text = rankdata[1].ToString();
                exploitText.text = p_data.exploit + "/" + rankdata[2];
                exploitText2.text = p_data.exploitNum.ToString();
                attackL.minimum = service_Attack;
                defenceL.minimum = service_Defense;
                kownledgeL.minimum = service_Kownledge;
                speedL.minimum = service_Speed;
                attackL.maximum = 9999;
                defenceL.maximum = 9999;
                kownledgeL.maximum = 9999;
                speedL.maximum = 9999;
                equip_attack = p_data.attack2 - p_data.attack;
                equip_defence = p_data.defense2 - p_data.defense;
                equip_kownledge = p_data.kownledge2 - p_data.kownledge;
                equip_speed = p_data.speed2 - p_data.speed;
                attack = p_data.attack;
                defence = p_data.defense;
                kownledge = p_data.kownledge;
                speed = p_data.speed;
                point = p_data.freePoints;
                if (point > 0)
                {
                    addpointBtn.enabled = true;
                    hasChangeNum = true;
                }
                else
                {
                    addpointBtn.enabled = false;
                    hasChangeNum = false;
                }
                if (p_data.rating < 100)
                {
                    expL.htmlText = p_data.exp + "/<color=#FCD402>" + p_data.levelExp + "</color>";
                }
                else
                {
                    expL.htmlText = p_data.exp + "/" + "max";
                }
                if (p_data.soldiers == null)
                {
                    armyPower.text = "0 / " + serviece_armyPower;
                    tipstr = ArmyPowerUtils.getArmyPowerTip(new ArmyPowerTipData()
                    {
                        armyPower = 0,
                        armyMaxPower = (int)p_data.armyMaxPower,
                        nextLv = p_data.rating + 1
                    });
                    ToolTipManager.getInstance().regTarget(armyPower.gameObject, tipstr);
                }
                addSoldierPanel(p_data.soldiers).Forget();
                setNumClickHandler();
                CastleVO currentCastle = MainModelLocator.getInstance().currentCastleData;
                nameL.htmlText = SetHerolevelUtils.setColor(p_data) + getHeroEstate(heroID, currentCastle.defenseHero);
                getType(heroData);
            }
        }

    }

    private async UniTaskVoid addSoldierPanel(List<List<int>> p_data)
    {
        GoldBorderUILoader sldUL = null;
        ISoldierBox sld = null;
        List<int> sldArray = new();
        for (int i = 0; i < 7; i++)
        {
            sldUL = win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/army" + i + "goldBorderUILoader").GetComponent<GoldBorderUILoader>();
            sld = SolidierNumPanelManager.getItem(sldUL);
            if (p_data != null)
            {
                if (i < p_data.Count)
                {
                    sld.source = ResPathManager.getRaceSoldierIcon(SoldierRaceUtils.getsoliderRace(p_data[i][0]), p_data[i][0], ResPathManager.RES_SIZE_M);
                    sld.value = p_data[i][1];
                    sldArray.Add(p_data[i][0]);
                }
                else
                {
                    sld.remove();
                    sld.source = await ResPathManager.getMenuIcon("A_0");
                }
            }
            else
            {
                sld.remove();
                sld.source = await ResPathManager.getMenuIcon("A_0");
            }
        }
        removeSldTip();
        objectPropertyProxy.getQueueByIdList(sldArray, addSldTips, ObjectPropertyProxy.GAMEOBJECT_TYPE_CLASS);
    }

    private void addSldTips(List<GameObjectVO> p_data)
    {
        GoldBorderUILoader sldUL = null;
        ISoldierBox sldbox = null;
        SoldierVO sldVo = null;
        string sldTip = null;
        float armyPowerNum = 0;
        Debug.Log("addSldTips p_data:" + p_data.Count);
        for (int i = 0; i < 7; i++)
        {
            sldUL = win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/army" + i + "goldBorderUILoader").GetComponent<GoldBorderUILoader>();
            sldbox = SolidierNumPanelManager.getItem(sldUL);
            if (i < p_data.Count)
            {
                sldVo = p_data[i] as SoldierVO;
                sldTip = ToolTipUtils.setSoldierTip(sldVo);
                ToolTipManager.getInstance().regTarget(sldUL.gameObject, sldTip);
                armyPowerNum += armyPowerCount(sldVo) * sldbox.value;
            }
        }
        armyPower.text = (int)armyPowerNum + " / " + serviece_armyPower;
        string tipstr = ArmyPowerUtils.getArmyPowerTip(new ArmyPowerTipData()
        {
            armyPower = (int)armyPowerNum,
            armyMaxPower = serviece_armyPower,
            nextLv = serviece_level + 1
        });
        ToolTipManager.getInstance().regTarget(armyPower.gameObject, tipstr);
    }

    private float armyPowerCount(SoldierVO p_data)
    {
        return ArmyPowerUtils.getsldAP(p_data);
    }

    private void removeSldTip()
    {
        GoldBorderUILoader sld = null;
        for (int i = 0; i < 7; i++)
        {
            sld = win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/army" + i + "goldBorderUILoader").GetComponent<GoldBorderUILoader>();
            ToolTipManager.getInstance().unRegTarget(sld.gameObject);
        }
    }


    private void setNumClickHandler()
    {
    }

    private string getHeroEstate(int p_data, int p_defenseHero)
    {
        if (p_data == p_defenseHero)
        {
            return "(" + LanguageManager.getScene("CONTENT_ARENA_DEFENSE_CITY") + ")";
        }
        return "(" + LanguageManager.getScene("CONTENT_ARENA_FREE") + ")";
    }

    private void getType(HeroVO heroVO)
    {
        if (heroVO.ShiledState == 1)
        {
            commonCheckBox1.selected = true;
        }
        else
        {
            commonCheckBox1.selected = false;
        }
    }

    public void listNotificationInterests()
    {
        MessageBus.SubscribeMany(new string[] { PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY, ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO, PopupWinEvent.CHANGEHERONAMEMEDIATOR_CREATE, ObjectPropertyProxy.MSG_OBJECT_UPDATE_CASTLE }, handleNotification);
    }

    public void removeListNotificationInterests()
    {
        MessageBus.UnsubscribeMany(new string[] { PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY, ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO, PopupWinEvent.CHANGEHERONAMEMEDIATOR_CREATE, ObjectPropertyProxy.MSG_OBJECT_UPDATE_CASTLE }, handleNotification);
    }

    public void handleNotification(string messageType, object data, string eventType)
    {
        switch (messageType)
        {
            case PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY:
                getHeroID((int)data);
                break;
            case ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO:

                getHeroVo(data);
                break;
            case PopupWinEvent.CHANGEHERONAMEMEDIATOR_CREATE:
                MessageBus.Publish(PopupWinEvent.CHANGEHERONAMEMEDIATOR_CREATE_COMPLETE, heroID);
                break;
        }
    }

    private string setfontColor(int p_data, string p_color)
    {
        return "<color=" + p_color + ">+" + p_data + "</color>";
    }

    public void onRemove()
    {
        removeListNotificationInterests();
        GoldBorderUILoader sld = null;
        ToolTipManager.getInstance().unRegTarget(attackL.gameObject);
        ToolTipManager.getInstance().unRegTarget(defenceL.gameObject);
        ToolTipManager.getInstance().unRegTarget(kownledgeL.gameObject);
        ToolTipManager.getInstance().unRegTarget(speedL.gameObject);
        ToolTipManager.getInstance().unRegTarget(armyPower.gameObject);
        ToolTipManager.getInstance().unRegTarget(corpsText.gameObject);
        ToolTipManager.getInstance().unRegTarget(exploitText.gameObject);
        for (int i = 0; i < 7; i++)
        {
            sld = win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/army" + i + "goldBorderUILoader").GetComponent<GoldBorderUILoader>();
            SolidierNumPanelManager.remove(sld);
        }
        removeSldTip();
        // attackL.removeEventListener(MouseEvent.CLICK, changePointHandler);
        // attackL.removeEventListener(Event.CHANGE, changePointHandler);
        // defenceL.removeEventListener(MouseEvent.CLICK, changePointHandler);
        // defenceL.removeEventListener(Event.CHANGE, changePointHandler);
        // speedL.removeEventListener(MouseEvent.CLICK, changePointHandler);
        // speedL.removeEventListener(Event.CHANGE, changePointHandler);
        // kownledgeL.removeEventListener(MouseEvent.CLICK, changePointHandler);
        // kownledgeL.removeEventListener(Event.CHANGE, changePointHandler);
        // addpointBtn.removeEventListener(MouseEvent.CLICK, addPointHandler);
        // changeNameBtn.removeEventListener(MouseEvent.CLICK, changeNameHandler);
        // fireOutBtn.removeEventListener(MouseEvent.CLICK, affirmFireHandler);
        // disbandBtn.removeEventListener(MouseEvent.CLICK, disbandHandler);
    }


    private bool hasChangeNum
    {
        set
        {
            attackL.Enabled = value;
            defenceL.Enabled = value;
            kownledgeL.Enabled = value;
            speedL.Enabled = value;
        }
    }

    private int attack
    {
        get => client_Attack;
        set
        {
            client_Attack = value;
            attackL.value = value;
            attackL.textField.text = value + setfontColor(equip_attack, "#63da4d");
        }
    }
    private int defence
    {
        get => client_Defense;
        set
        {
            client_Defense = value;
            defenceL.value = value;
            defenceL.textField.text = value + setfontColor(equip_defence, "#63da4d");
        }
    }
    private int speed
    {
        get => client_Speed;
        set
        {
            client_Speed = value;
            speedL.value = value;
            speedL.textField.text = value + setfontColor(equip_speed, "#63da4d");
        }
    }
    private int kownledge
    {
        get => client_Kownledge;
        set
        {
            client_Kownledge = value;
            kownledgeL.value = value;
            kownledgeL.textField.text = value + setfontColor(equip_kownledge, "#63da4d");
        }
    }
    private int point
    {
        get => client_FreePoints;
        set
        {
            client_FreePoints = value;
            pointL.htmlText = "<color=#63da4d>" + value + "</color>";
        }
    }


    private CommonTextArea nameL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/nameText").GetComponent<CommonTextArea>();
        }
    }

    private CommonTextArea raceL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/raceText").GetComponent<CommonTextArea>();
        }
    }

    private CommonTextArea levelL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/levelText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea expL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/expText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea pointL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/pointText").GetComponent<CommonTextArea>();
        }
    }
    private SmallButton addpointBtn
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/pointButton").GetComponent<SmallButton>();
        }
    }
    private CommonNumericStepper attackL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/attackNumericStepper").GetComponent<CommonNumericStepper>();
        }
    }
    private CommonNumericStepper defenceL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/defendNumericStepper").GetComponent<CommonNumericStepper>();
        }
    }
    private CommonNumericStepper speedL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/speedNumericStepper").GetComponent<CommonNumericStepper>();
        }
    }
    private CommonNumericStepper kownledgeL
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/commonNumericStepper4").GetComponent<CommonNumericStepper>();
        }
    }
    private CommonButton changeNameBtn
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/commonButton1").GetComponent<CommonButton>();
        }
    }
    private CommonButton fireOutBtn
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/dismissButton").GetComponent<CommonButton>();
        }
    }
    private CommonButton disbandBtn
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/assignButton").GetComponent<CommonButton>();
        }
    }
    private CommonButton changeRaceBtn
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/commonButton72").GetComponent<CommonButton>();
        }
    }
    private CommonTextArea armyPower
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/armyText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea corpsText
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/corpsText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea exploitText
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/exploitText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea exploitText2
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/exploitText2").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea exploitTextL2
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/amryBorderPanel/commonLabel9").GetComponent<CommonTextArea>();
        }
    }
    private SmallButton washPointsBtn
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/smallButton3").GetComponent<SmallButton>();
        }
    }
    private CommonCheckBox commonCheckBox1
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel/pointBorderPa/commonCheckBox1").GetComponent<CommonCheckBox>();
        }
    }
}