using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Pb;
using SuperScrollView;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class HeroEquipmentMediator
{
    public const string NAME = "HeroEquipmentMediator";
    private PopupWinFrame win;
    public const string EQUIPMENT_PIC = "E_0";


    private const string TYPE_WEAPON = "TYPE_WEAPON";

    private const string TYPE_PROTECT = "TYPE_PROTECT";

    private const string TYPE_JEWELRY = "TYPE_JEWELRY";

    private const string TYPE_ALL = "TYPE_ALL";

    private string now_type;

    private Dictionary<int, EquipmentVO> equipmentVO_list = new();

    private List<int> itemsID_list = new();

    private List<EquipmentVO> weapon_link = new();

    private List<EquipmentVO> protect_link = new();

    private List<EquipmentVO> jewelry_link = new();

    private List<EquipmentVO> all_link = new();

    private int hero_id;

    private HeroVO hero_vo;

    private List<int> hero_items = new();

    private float enableAlpha = 0.5f;

    private ObjectPropertyProxy objectPropertyProxy;
    private GoodsProxy goodsProxy;

    private bool once = false;

    private ActionObject action_Obj;

    private PlayerVO playervo;



    //   private var key:KeyUtils;

    public HeroEquipmentMediator(PopupWinFrame viewObject)
    {
        win = viewObject;
        listNotificationInterests();
        now_type = TYPE_WEAPON;
        init();
        initListener();
    }

    private void init()
    {
        actionBtn.label = LanguageManager.getScene("LABEL_TM_EQU");
        powerBtn.label = LanguageManager.getScene("LABEL_TM_POWERSUPPLY");
        actionBtn.OnClick.AddListener((btn) =>
        {
            actionHandler();
        });
        // actionBtn.addEventListener(MouseEvent.CLICK,actionHandler,false,0,true);
        insertBoxDataByType(TYPE_WEAPON);
        objectPropertyProxy = ProxyManager.Instance.objectPropertyProxy;
        goodsProxy = ProxyManager.Instance.goodsProxy;
        goodsProxy.getPlayerEquipmentBySort(getEquipmentSortByType(now_type));
        // 如果目标对象没有 EventTrigger 组件，添加一个
        // EventTrigger eventTrigger = actionStage.GetComponent<EventTrigger>();
        // if (eventTrigger == null)
        // {
        //     eventTrigger = actionStage.gameObject.AddComponent<EventTrigger>();
        // }
        // // 添加鼠标点击事件
        // actionStage.AddEventTrigger(eventTrigger, EventTriggerType.PointerClick, hideBtnHandler);
        hideBtnHandler(win.token).Forget();

    }

    private async UniTaskVoid hideBtnHandler(CancellationToken token)
    {
        try
        {
            while (!token.IsCancellationRequested)
            {
                // 高效等待鼠标点击（无输入时不消耗性能,支持移动端）
                await UniTask.WaitUntil(() => Input.GetMouseButtonDown(0) || Input.touchCount > 0,
                    PlayerLoopTiming.Update,
                    token);
                // 等待一帧让UI系统更新
                await UniTask.Yield();

                // 检查是否点击在UI上
                var results = RaycastUI();
                if (results.Count == 0)
                {
                    continue;
                }

                // 处理所有点击到的UI元素
                bool foundEquipment = false;
                foreach (var result in results)
                {
                    // 检测EquipmentBorder组件
                    if (result.gameObject.TryGetComponent<EquipmentBorder>(out _))
                    {
                        // Debug.Log($"检测到装备边框: {result.gameObject.name}");
                        foundEquipment = true;
                        break; // 找到一个即可
                    }
                }
                if (!foundEquipment)
                {
                    // Debug.Log("点击在UI上，但不是EquipmentBorder");
                    actionSP.visible = false;
                }
                // 避免连续检测过于频繁
                await UniTask.DelayFrame(1, cancellationToken: token);
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消
            Debug.Log("DetectClicksAsync canceled");
        }
    }

    private List<RaycastResult> RaycastUI()
    {
        var eventData = new PointerEventData(win._eventSystem)
        {
            position = Input.mousePosition,
            pressPosition = Input.mousePosition
        };

        var results = new List<RaycastResult>();
        win._raycaster.Raycast(eventData, results);

        return results;
    }

    private GameObject GetTopmostClickedUI()
    {
        var results = RaycastUI();
        if (results.Count == 0) return null;

        // 按深度排序（深度值越大越在上层）
        results.Sort((a, b) => b.depth.CompareTo(a.depth));
        return results[0].gameObject;
    }

    private void initListener()
    {
        allButton.OnClick.AddListener((btn) =>
        {
            allHandler();
        });
        weaponButton.OnClick.AddListener((btn) =>
        {
            weaponHandler();
        });
        protectButton.OnClick.AddListener((btn) =>
        {
            protectHandler();
        });
        jewelryButton.OnClick.AddListener((btn) =>
        {
            jewelryHandler();
        });
    }

    private void allHandler()
    {
        if (now_type == TYPE_ALL)
        {
            return;
        }
        if (all_link.Count == 0)
        {
            goodsProxy.getPlayerEquipmentBySort(getEquipmentSortByType(TYPE_ALL));
        }
        insertBoxDataByType(TYPE_ALL);
    }

    private void weaponHandler()
    {
        if (now_type == TYPE_WEAPON)
        {
            return;
        }
        if (weapon_link.Count == 0)
        {
            goodsProxy.getPlayerEquipmentBySort(getEquipmentSortByType(TYPE_WEAPON));
        }
        insertBoxDataByType(TYPE_WEAPON);
    }

    private void protectHandler()
    {
        if (now_type == TYPE_PROTECT)
        {
            return;
        }
        if (protect_link.Count == 0)
        {
            goodsProxy.getPlayerEquipmentBySort(getEquipmentSortByType(TYPE_PROTECT));
        }
        insertBoxDataByType(TYPE_PROTECT);
    }

    private void jewelryHandler()
    {
        if (now_type == TYPE_JEWELRY)
        {
            return;
        }
        if (jewelry_link.Count == 0)
        {
            goodsProxy.getPlayerEquipmentBySort(getEquipmentSortByType(TYPE_JEWELRY));
        }
        insertBoxDataByType(TYPE_JEWELRY);
    }

    private void actionHandler()
    {
        actionSP.visible = false;
        if (hero_vo == null)
        {
            return;
        }
        if (action_Obj == null)
        {
            return;
        }
        int index = hero_vo.items.IndexOf(action_Obj.id);
        if (index != -1)
        {
            if (index != 5 && index != 6)
            {
                Alert.show(LanguageManager.getScene("CONTENT_HE_HAS_SAME"));
                ToolTipManager.getInstance().removeToolTip();
                return;
            }
        }
        if (action_Obj.level > hero_vo.rating)
        {
            Alert.show(LanguageManager.getScene("CONTENT_HE_LEVEL_LOW"));
            ToolTipManager.getInstance().removeToolTip();
            return;
        }
        sendUpdateEquipment();
        insertBoxDataByType(now_type);
    }

    private void sendUpdateEquipment()
    {
        //  var send:MessageByteArray = new MessageByteArray();
        //  send.writeCustomMsg(4);
        //  send.writeCustomInt(SocketProxy.CMTS_HERO_EQUIPMENT);
        //  send.writeCustomInt(hero_id);
        //  send.writeCustomInt(Number(action_Obj.id));
        //  send.writeCustomInt(Number(action_Obj.part));
        //  SocketManager.getInstance().send(send);
    }

    private void parseHeroVO(object p_data)
    {
        HeroVO temp_vo = null;
        if (p_data != null && p_data is HeroVO)
        {
            temp_vo = p_data as HeroVO;
        }
        else if (p_data != null && p_data is List<GameObjectVO>)
        {
            temp_vo = (p_data as List<GameObjectVO>)[0] as HeroVO;
        }
        else
        {
            return;
        }
        hero_vo = new();
        hero_vo = temp_vo;
        setHeroInfo();
        if (!once)
        {
            once = true;
            // objectPropertyProxy.getObjectPropertyCallBack(PlayerIdUtils._playId, parseEquipmentList);
        }
        // resetToolTip();
    }

    private void resetToolTip()
    {
        int len = 0;
        EquipmentBorder temp = null;
        int i = 0;
        if (item_box != null)
        {
            len = item_box.childCount;
            for (i = 0; i < len; i++)
            {
                temp = item_box.GetChild(i).GetComponent<EquipmentBorder>();
                if (temp == null)
                {
                    break;
                }
                if (temp.equipmentvo is null)
                {
                    break;
                }
                ToolTipManager.getInstance().unRegTarget(temp.gameObject);
                ToolTipManager.getInstance().regTarget(temp.gameObject, getToolTipContent(temp.equipmentvo));
            }
        }
    }

    private void setHeroInfo()
    {
        if (hero_vo != null)
        {
            attackLabel.htmlText = hero_vo.attack + "<color=#63da4d>+" + (hero_vo.attack2 - hero_vo.attack) + "</color>";
            defendLabel.htmlText = hero_vo.defense + "<color=#63da4d>+" + (hero_vo.defense2 - hero_vo.defense) + "</color>";
            speedLabel.htmlText = hero_vo.speed + "<color=#63da4d>+" + (hero_vo.speed2 - hero_vo.speed) + "</color>";
            knowledgeLabel.htmlText = hero_vo.kownledge + "<color=#63da4d>+" + (hero_vo.kownledge2 - hero_vo.kownledge) + "</color>";
        }
    }

    private void parseEquipmentList(object p_data)
    {
        PlayerVO temp = null;
        int i = 0;
        if (p_data != null)
        {
            temp = MainModelLocator.getInstance().playerInfo;
            if (playervo != null)
            {
                if (ListUtils.Compare(temp.items, playervo.items))
                {
                    return;
                }
            }
            playervo = temp;
            if (playervo.items != null)
            {
                itemsID_list = new();
                for (i = 0; i < playervo.items.Count; i++)
                {
                    itemsID_list.Add(playervo.items[i].TargetId);
                }
                objectPropertyProxy.getQueueByIdList(itemsID_list.FindAll((_id) =>
                {
                    if (GoodsProxy.goodInfo_map.ContainsKey(_id))
                    {
                        if (GoodsProxy.goodInfo_map[_id].part <= 13)
                        {
                            return true;
                        }
                    }
                    else
                    {
                        return true;
                    }
                    return false;

                }), parseEquipmentVOList, ObjectPropertyProxy.GAMEOBJECT_TYPE_CLASS, true);
            }
            else
            {
                equipmentVO_list = new();
                weapon_link = new();
                protect_link = new();
                jewelry_link = new();
                all_link = new();
                insertBoxDataByType(now_type);
            }
        }
    }

    private void parseEquipmentVOList(List<GameObjectVO> p_data)
    {
        List<EquipmentVO> temp_ary = null;
        EquipmentVO temp_vo = null;
        int i = 0;
        if (p_data != null)
        {
            equipmentVO_list = new();
            weapon_link = new();
            protect_link = new();
            jewelry_link = new();
            all_link = new();
            temp_ary = p_data.ConvertAll(item => item as EquipmentVO).FindAll(item => item != null);
            for (i = 0; i < temp_ary.Count; i++)
            {
                temp_vo = temp_ary[i];
                equipmentVO_list.Add(temp_vo.targetId, temp_vo);
                sortByType(temp_ary[i]);
            }
            Debug.Log("weapon_link: " + weapon_link.Count);
            insertBoxDataByType(now_type);
        }
    }

    private void sortByType(EquipmentVO _vo)
    {
        switch (_vo.part)
        {
            case EquipmentProxy.TYPE_WEAPON:
                getHasArray(weapon_link, _vo);
                break;
            case EquipmentProxy.TYPE_ARMOR:
            case EquipmentProxy.TYPE_CAP:
            case EquipmentProxy.TYPE_SHOES:
            case EquipmentProxy.TYPE_GLOVE:
                getHasArray(protect_link, _vo);
                break;
            case EquipmentProxy.TYPE_RING:
            case EquipmentProxy.TYPE_NECKLACE:
                getHasArray(jewelry_link, _vo);
                break;
        }
        if (_vo.part >= 7 && _vo.part <= 13)
        {
            getHasArray(all_link, _vo);
        }
    }

    private void getHasArray(List<EquipmentVO> _link, EquipmentVO _vo)
    {
        _link.Add(_vo);
    }

    private void insertBoxDataByType(string _type)
    {
        List<EquipmentVO> link = null;
        int box_child_len = 0;
        int len = 0;
        int type_length = 0;
        if (!string.IsNullOrEmpty(_type))
        {
            clearBox();
            equipmentScrollPane.SetListItemCount(0, false);
            equipmentScrollPane.RefreshAllShownItem();
            equipmentScrollPane.ResetInitState();
            now_type = _type;
            changeButtonEnable();
            link = getAryLinkByType();
            if (link != null)
            {
                box_child_len = item_box.childCount;
                if (link.Count < 16)
                {
                    len = 16;
                }
                else
                {
                    len = link.Count;
                }

                Debug.Log("len: " + len);
                type_length = link.Count;
                equipmentScrollPane.InitGridView(len, (gridView, _index, row, column) =>
                {
                    LoopGridViewItem item = gridView.NewListViewItem("EquipmentBorder");
                    var localUILoader = item.GetComponent<EquipmentBorder>();
                    if (_index < link.Count)
                    {
                        localUILoader.equipmentvo = link[_index];
                        localUILoader.source = ResPathManager.getPropIcon(localUILoader.equipmentvo.typeId, ResPathManager.RES_SIZE_M);
                        localUILoader.useHandCursor = true;
                        localUILoader.buttonMode = true;
                        ToolTipManager.getInstance().regTarget(localUILoader.gameObject, " ");
                        localUILoader.OnEnter_EB.AddListener((_eb) =>
                        {
                            GoodsInfoBindAsync.showTipHandler(localUILoader.gameObject, localUILoader.equipmentvo.targetId, NAME);
                        });
                        addBorderListener(localUILoader);
                    }
                    else
                    {
                        // Handle async operation without blocking - fire and forget
                        ResPathManager.getMenuIcon(EQUIPMENT_PIC, ResPathManager.RES_SIZE_M, (sprite) =>
                        {
                            localUILoader.source = sprite;
                        });
                    }
                    return item;
                });
            }
        }
    }


    private List<EquipmentVO> getAryLinkByType()
    {
        List<EquipmentVO> return_ary = null;
        switch (now_type)
        {
            case TYPE_ALL:
                return_ary = all_link;
                break;
            case TYPE_WEAPON:
                return_ary = weapon_link;
                break;
            case TYPE_JEWELRY:
                return_ary = jewelry_link;
                break;
            case TYPE_PROTECT:
                return_ary = protect_link;
                break;
        }
        return return_ary;
    }

    private string getToolTipContent(EquipmentVO vo)
    {
        return ToolTipUtils.equipmentTip(vo, new(), true, hero_vo.rating, hero_vo.rank, hero_vo.exploitNum);
    }

    private void clearBox()
    {
        EquipmentBorder border = null;
        int len = item_box.childCount;
        while (len > 0)
        {
            border = item_box.GetChild(0).GetComponent<EquipmentBorder>();
            ToolTipManager.getInstance().unRegTarget(border.gameObject);
            removeBorderListener(border);
            // border.equipmentvo = null;
            // border.source = null;
            // border = null;
            // PrefabManager.Instance.DestroyObject(border.gameObject);
            len--;
        }
    }

    private void addBorderListener(EquipmentBorder _sp)
    {
        _sp.OnClick_EB.AddListener((eb) =>
        {
            clickHandler(eb);
        });
    }

    private void removeBorderListener(EquipmentBorder _sp)
    {
        _sp.OnClick_EB.RemoveListener((eb) =>
        {
            clickHandler(eb);
        });
    }

    private void clickHandler(EquipmentBorder evt)
    {
        EquipmentVO vo = null;
        ChatProxy chatproxy = null;
        EquipmentBorder _tgr = evt;
        vo = _tgr.equipmentvo;

        int part = vo.part;
        if (vo.hero_rank == 0)
        {
            powerBtn.visible = false;
        }
        else
        {
            powerBtn.visible = true;
        }
        switch (vo.part)
        {
            case 5:
                if (hero_vo.items[5] > 0)
                {
                    if (hero_vo.items[6] > 0)
                    {
                        part = 5;
                    }
                    else
                    {
                        part = 6;
                    }
                }
                else
                {
                    part = 5;
                }
                break;
            case 6:
                part = 7;
                break;
            case 7:
                part = 8;
                break;
            case 8:
                part = 9;
                break;
            case 9:
                part = 11;
                break;
            case 10:
                if (hero_vo.items[12] > 0)
                {
                    if (hero_vo.items[13] > 0)
                    {
                        part = 12;
                    }
                    else
                    {
                        part = 13;
                    }
                }
                else
                {
                    part = 12;
                }
                break;
            case 11:
                part = 14;
                break;
            case 12:
                part = 15;
                break;
            case 13:
                part = 10;
                break;
        }
        action_Obj = new()
        {
            level = vo.heroLevel,
            id = vo.targetId,
            part = part,
            name = vo.name,
            color = ToolTipUtils.equipmentColor(vo)
        };
        // Vector2 mousePosition = Input.mousePosition;
        // Vector2 adjustedPosition = mousePosition;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(win.winBodyRect, Input.mousePosition, Camera.main, out Vector2 localPoint);
        actionSP.SetPosition(localPoint.x + 12, localPoint.y);
        // actionSP.x = win.mouseX + win.x + 12;
        // actionSP.y = win.mouseY + win.y;
        // if (key.isDown(Keyboard.CONTROL))
        // {
        //     chatproxy = ProxyManager.Instance.chatProxy;
        //     chatproxy.equipmentShowChat(vo);
        // }
        ToolTipManager.getInstance().removeToolTip();
        actionSP.visible = true;
    }

    private void changeButtonEnable()
    {
        allButton.alpha = enableAlpha;
        weaponButton.alpha = enableAlpha;
        jewelryButton.alpha = enableAlpha;
        protectButton.alpha = enableAlpha;
        switch (now_type)
        {
            case TYPE_ALL:
                allButton.alpha = 1;
                break;
            case TYPE_WEAPON:
                weaponButton.alpha = 1;
                break;
            case TYPE_JEWELRY:
                jewelryButton.alpha = 1;
                break;
            case TYPE_PROTECT:
                protectButton.alpha = 1;
                break;
        }
    }

    private int getEquipmentSortByType(string type)
    {
        switch (type)
        {
            case TYPE_ALL:
                return 4;
            case TYPE_WEAPON:
                return 1;
            case TYPE_PROTECT:
                return 2;
            case TYPE_JEWELRY:
                return 3;
            default:
                return 0;
        }
    }

    private string getTypeByEquipmentSort(int sort)
    {
        switch (sort)
        {
            case 4:
                return TYPE_ALL;
            case 1:
                return TYPE_WEAPON;
            case 2:
                return TYPE_PROTECT;
            case 3:
                return TYPE_JEWELRY;
            default:
                return TYPE_ALL;
        }
    }

    private void parsePlayerEquipment(object p_data)
    {
        List<object> data = p_data as List<object>;
        int sort = (int)data[0];
        switch (sort)
        {
            case 0:
                equipmentVO_list = new();
                weapon_link = new();
                protect_link = new();
                jewelry_link = new();
                all_link = new();
                break;
            case 1:
                weapon_link = new();
                break;
            case 2:
                protect_link = new();
                break;
            case 3:
                jewelry_link = new();
                break;
            case 4:
                all_link = new();
                break;
            default:
                return;
        }
        List<EquipmentSimple> equipment = data[1] as List<EquipmentSimple>;
        EquipmentVO temp_vo = null;
        foreach (var item in equipment)
        {
            temp_vo = new()
            {
                targetId = item.TargetId,
                typeId = item.TypeId,
                part = GoodsProxy.goodInfo_map[item.TypeId].part
            };
            temp_vo.targetId = item.TargetId;
            sortByType(temp_vo);
        }
        if (now_type == getTypeByEquipmentSort(sort))
        {
            insertBoxDataByType(now_type);
        }
    }



    public void onRemove()
    {
        removeListNotificationInterests();
        removeListener();
        delObj();
    }

    private void removeListener()
    {
        allButton.OnClick.RemoveAllListeners();
        weaponButton.OnClick.RemoveAllListeners();
        protectButton.OnClick.RemoveAllListeners();
        jewelryButton.OnClick.RemoveAllListeners();
        // win.stage.removeEventListener(MouseEvent.CLICK, hideBtnHandler);
    }

    private void delObj()
    {
        actionBtn.visible = false;
        actionSP.visible = false;
        clearBox();
        equipmentScrollPane.SetListItemCount(0, false);
        equipmentScrollPane.RefreshAllShownItem();
        equipmentScrollPane.ResetInitState();
        equipmentVO_list.Clear();
        equipmentVO_list = null;
        weapon_link.Clear();
        weapon_link = null;
        protect_link.Clear();
        protect_link = null;
        jewelry_link.Clear();
        jewelry_link = null;
        all_link.Clear();
        all_link = null;
        hero_vo = null;
        hero_items.Clear();
        hero_items = null;
        itemsID_list = null;
        // item_box = null;
        objectPropertyProxy = null;
        goodsProxy = null;
        action_Obj = null;
        playervo = null;
        GoodsInfoBindAsync.clearGoodsSceneMap(NAME);
    }

    private void listNotificationInterests()
    {
        MessageBus.SubscribeMany(new string[] { PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY, ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO, ObjectPropertyProxy.MSG_OBJECT_UPDATE_PLAYER, GoodsProxy.MSG_LOAD_PLAYER_EQUIPMENT }, handleNotification);
    }

    private void removeListNotificationInterests()
    {
        MessageBus.UnsubscribeMany(new string[] { PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY, ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO, ObjectPropertyProxy.MSG_OBJECT_UPDATE_PLAYER, GoodsProxy.MSG_LOAD_PLAYER_EQUIPMENT }, handleNotification);
    }

    public void handleNotification(string messageType, object data, string eventType)
    {
        switch (messageType)
        {
            case PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY:
                if (data != null)
                {
                    hero_id = (int)data;
                    objectPropertyProxy.getObjectPropertyCallBack(hero_id, parseHeroVO);
                }
                break;
            case ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO:
                parseHeroVO(data);
                break;
            case ObjectPropertyProxy.MSG_OBJECT_UPDATE_PLAYER:
                parseEquipmentList(data);
                break;
            case GoodsProxy.MSG_LOAD_PLAYER_EQUIPMENT:
                parsePlayerEquipment(data);
                break;
        }
    }

    private CommonButton allButton
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/allButton").GetComponent<CommonButton>();
        }
    }

    private CommonButton weaponButton
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/weaponButton").GetComponent<CommonButton>();
        }
    }
    private CommonButton protectButton
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/protectButton").GetComponent<CommonButton>();
        }
    }
    private CommonButton jewelryButton
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/jewelryButton").GetComponent<CommonButton>();
        }
    }
    private LoopGridView equipmentScrollPane
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/equipmentScrollPane/Scroll View").GetComponent<LoopGridView>();
        }
    }

    private Transform item_box
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/equipmentScrollPane/Scroll View/Viewport/Content").transform;
        }
    }
    private CommonTextArea attackLabel
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/rightCornerBorderPanel5/attackText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea defendLabel
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/rightCornerBorderPanel5/defendText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea speedLabel
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/rightCornerBorderPanel5/speedText").GetComponent<CommonTextArea>();
        }
    }
    private CommonTextArea knowledgeLabel
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel/rightCornerBorderPanel5/commonTextArea17").GetComponent<CommonTextArea>();
        }
    }

    private SmallButton actionBtn
    {
        get
        {
            return win.FindObjectByPath("win/actionLayer/actionSP/actionBtn").GetComponent<SmallButton>();
        }
    }

    private SmallButton powerBtn
    {
        get
        {
            return win.FindObjectByPath("win/actionLayer/actionSP/powerBtn").GetComponent<SmallButton>();
        }
    }

    private ScaleSprite actionSP
    {
        get
        {
            return win.FindObjectByPath("win/actionLayer/actionSP").GetComponent<ScaleSprite>();
        }
    }

    private ScaleSprite actionStage
    {
        get
        {
            return win.FindObjectByPath("win/actionLayer/actionStage").GetComponent<ScaleSprite>();
        }
    }




    private class ActionObject
    {
        public int level;
        public int id;
        public int part;
        public string name;
        public string color;
    }
}


