using UnityEngine;
public class PrefabManager : MonoBehaviour
{


    public const string SeegoodCellPrefabPath = "prefabs/popupwin/kingdom/SeegoodCell";
    public GameObject seegoodCellPrefab;
    public const string LockIconPrefabPath = "prefabs/popupwin/kingdom/kingdomCom/LockIcon";
    public GameObject lockIconPrefab;

    public RectTransform mainUIRect;
    public Canvas rootCanvas;

    private static PrefabManager instance;
    public static PrefabManager Instance
    {
        get
        {
            if (instance == null)
            {
                // 在场景中查找 ToolTipManager
                instance = FindFirstObjectByType<PrefabManager>();
                if (instance == null)
                {
                    // 如果不存在，则创建一个新的 GameObject 并附加 ToolTipManager
                    GameObject obj = new("PrefabManager");
                    instance = obj.AddComponent<PrefabManager>();
                }
            }
            return instance;
        }
    }
    private void Awake()
    {

        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }



    public GameObject LoadPrefab(string prefabPath)
    {
        string path = prefabPath;
        GameObject prefab = Resources.Load<GameObject>(path);
        if (prefab == null)
        {
            Debug.LogError($"Prefab not found at path: {path}");
            return null;
        }
        return prefab;
    }

    // 通用创建对象方法
    public GameObject InstantitateObject(GameObject obj, Transform parent = null)
    {
        if (obj == null)
        {
            Debug.LogError("Prefab is null, cannot instantiate.");
            return null;
        }

        // 如果没有指定父物体，则使用当前物体作为父物体
        if (parent == null)
        {
            return Instantiate(obj);
        }

        // 实例化预制体并设置父物体
        return Instantiate(obj, parent);
    }

    // 通用销毁对象方法
    public void DestroyObject(GameObject obj)
    {
        if (obj != null)
        {
            Destroy(obj);
        }
    }

}