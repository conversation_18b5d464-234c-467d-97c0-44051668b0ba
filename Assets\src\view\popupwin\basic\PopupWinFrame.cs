using System.Collections;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class PopupWinFrame : ScaleSprite, IPopUpWin, IPointerClickHandler
{
    public object data;
    private float minTitleW = 190;
    private float maxTitleW = 250;
    private float winH = 200;
    private float winW = 300;
    private float minWinW = 350;
    private float minWinH = 200;
    private float winEffectDelay = 0.5f;
    private float buttonPadding = 62;
    private string _winName = "";
    private string _winTitle = "";
    private int offsetX = 0;
    private int offsetY = 0;

    private bool _isCenter = true;
    private bool _isModal = false;

    public ScaleSprite modalLayer;
    public ScaleSprite win;
    public RectTransform winBodyRect;
    public ScaleSprite frameTitleBg;
    public ScaleSprite frameBg;

    public ScaleSprite buttonPanel;

    public SimpleButton closeBtn;

    public ScaleSprite contentPanel;

    private SimpleButton btn2;

    private SimpleButton helpBtn;

    public string viewMediatorName;

    public CommonTextMeshPro titleText;

    public List<GameObject> buttonList = new();

    private List<GameObject> toolTipList;

    public WinXML winXml;

    private bool isDragging = false;

    public UnityEvent OnOpen { get; private set; } = new();
    public UnityEvent OnClose { get; private set; } = new();

    private Vector2 dragOffset;

    public Camera mainCamera;

    private float overflowFactor = 1.5f; // 允许弹窗超出的比例

    public CancellationToken token;
    private CancellationToken dragToken;
    public EventSystem _eventSystem;
    public GraphicRaycaster _raycaster;
    public Canvas _rootCanvas;

    protected override void Awake()
    {
        base.Awake();
        alpha = 0;
        __init();
        __createUI();
        parseWinXML();
        init();
        createContent(contentPanel);
        createButton(buttonPanel);
        Debug.Log("PopupWinFrame Awake");
    }

    private void Start()
    {
        mainCamera = Camera.main; // 缓存 Camera.main
        _eventSystem = EventSystem.current;
        _rootCanvas = PrefabManager.Instance.rootCanvas;
        _raycaster = _rootCanvas.GetComponent<GraphicRaycaster>();
        token = this.GetCancellationTokenOnDestroy();
    }

    IEnumerator MyCoroutine()
    {
        // 暂停协程2秒
        yield return new WaitForSeconds(5f);
        show();
    }

    private void __init()
    {
        // buttonList = new();
        toolTipList = new();
        // componentObj = new Object();
        // parentPanelObjList = new Array();
        // componentObjList = new Array();
        // parentPanelObj = new Object();
        // parentStaticMap = new Map();
    }

    protected void init()
    {
    }

    protected void createContent(ScaleSprite p_contentPanel)
    {
    }

    protected void createButton(ScaleSprite p_buttonPanel)
    {
    }

    private void __createUI()
    {
        titleText.text = winTitle;
        EventTrigger frameBgEventTrigger = frameBg.GetComponent<EventTrigger>();
        AddEventTrigger(frameBgEventTrigger, EventTriggerType.PointerDown, mouseDownHandler);
        AddEventTrigger(frameBgEventTrigger, EventTriggerType.PointerUp, mouseUpHandler);
        AddEventTrigger(frameBgEventTrigger, EventTriggerType.Drag, mouseDragHandler);

        EventTrigger frameTitleBgEventTrigger = frameTitleBg.GetComponent<EventTrigger>();
        AddEventTrigger(frameTitleBgEventTrigger, EventTriggerType.PointerDown, mouseDownHandler);
        AddEventTrigger(frameTitleBgEventTrigger, EventTriggerType.PointerUp, mouseUpHandler);
        AddEventTrigger(frameTitleBgEventTrigger, EventTriggerType.Drag, mouseDragHandler);

        closeBtn.OnClick.AddListener((SimpleButton btn) =>
        {
            closeHandler();
        });
    }

    public void parseWinXML(WinXML p_winXML = null)
    {
        if (p_winXML == null)
        {
            return;
        }
        winXml = p_winXML;
        // parentPanelObj = new Object();
        // var bodyLen:int = xml.body.child("contorls").length();
        // var bottomLen:int = xml.bottom.child("contorls").length();
        // var moduleLen:int = xml.ModuleList.child("module").length();
        winName = winXml.winType;
        winTitle = LanguageManager.getPopupWin(winXml.winName);
        width = winXml.width;
        height = winXml.height;
        // for (var i:int = 0; i < bodyLen; i++)
        //  {
        //     xmlObj = parseContorlsAttribute(xml.body.contorls[i]);
        //     parseXMLContorlsNode(xmlObj);
        // }
        // for (var j:int = 0; j < bottomLen; j++)
        //  {
        //     xmlObj = parseContorlsAttribute(xml.bottom.contorls[j]);
        //     parseXMLContorlsNode(xmlObj);
        // }
        updateButtonPosition();
    }

    private void updateButtonPosition()
    {
        float btnLength;
        int i;
        int showNum;
        int spaceNum;
        float spaceW;
        float currentX;
        if (buttonList.Count > 0)
        {
            btnLength = 0;
            i = 0;
            showNum = 0;
            for (i = 0; i < buttonList.Count; i++)
            {
                ScaleSprite button = buttonList[i].GetComponent<ScaleSprite>();
                if (button.visible)
                {
                    btnLength += button.width;
                    showNum++;
                }
            }
            spaceNum = showNum - 1 + 2;
            spaceW = (winW - buttonPanel.x * 2 - btnLength) / spaceNum;
            currentX = spaceW;
            for (i = 0; i < buttonList.Count; i++)
            {
                ScaleSprite button = buttonList[i].GetComponent<ScaleSprite>();
                if (button.visible)
                {
                    button.x = currentX;
                    button.y = -13;
                    currentX += button.width + spaceW;
                }
                if (!buttonList[i].transform.IsChildOf(buttonPanel.transform))
                {
                    buttonList[i].transform.SetParent(buttonPanel.transform);
                    if (buttonList[i].GetComponent<LabelButton>() != null)
                    {
                        button.OnShow.AddListener(onBottomButtonUpdate);
                        button.OnHide.AddListener(onBottomButtonUpdate);
                    }
                }
            }
        }
    }

    private void onBottomButtonUpdate()
    {
        updateButtonPosition();
    }

    private void mouseDownHandler(BaseEventData data)
    {
        PopUpWinManager.getInstance().bringToFront(this);
        // 开始拖动时记录初始偏移
        Vector2 mousePosition = Input.mousePosition;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
        rectTransform,
        mousePosition,
        mainCamera,
        out Vector2 localPoint
    );
        // 计算鼠标与窗口初始位置的偏移
        dragOffset = localPoint - (Vector2)win.rectTransform.localPosition;
        isDragging = true;
        win.alpha = 0.5f;
        // OptimizedDragLoop();
    }

    private void mouseUpHandler(BaseEventData data)
    {
        isDragging = false;
        win.alpha = 1;
        // dragToken = new CancellationTokenSource().Token;
    }

    private void mouseDragHandler(BaseEventData data)
    {
        if (isDragging)
        {
            Vector2 mousePosition = Input.mousePosition;
            // Debug.Log("mousePosition: " + mousePosition);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform,
                mousePosition,
                mainCamera,
                out Vector2 localPoint
            );
            // 应用初始偏移补偿
            Vector2 targetPosition = localPoint - dragOffset;
            // Debug.Log("targetPosition: " + targetPosition);
            // 限制弹窗位置到父容器范围内
            Vector2 clampedPosition = ClampToParentBounds(targetPosition);
            win.rectTransform.localPosition = clampedPosition;
        }
    }

    // 进一步优化的拖动方案 - 使用 UniTask 的帧更新
    private async UniTaskVoid OptimizedDragLoop()
    {
        try
        {
            while (isDragging && dragToken.IsCancellationRequested == false)
            {
                Vector2 mousePosition = Input.mousePosition;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform,
                mousePosition,
                mainCamera,
                out Vector2 localPoint
            );
                Vector2 targetPosition = localPoint - dragOffset;
                Vector2 clampedPosition = ClampToParentBounds(targetPosition);
                win.rectTransform.localPosition = clampedPosition;

                // 每2帧更新一次，减少更新频率
                await UniTask.DelayFrame(2, cancellationToken: dragToken);
            }
        }
        catch (System.OperationCanceledException)
        {
            // Token 被取消时正常退出
        }
    }

    private Vector2 ClampToParentBounds(Vector2 position)
    {
        Rect parentRect = rectTransform.rect;
        Rect windowRect = win.rectTransform.rect;

        // 根据 overflowFactor 调整边界
        // float xMin = parentRect.xMin - windowRect.width * overflowFactor;
        // float xMax = parentRect.xMax + windowRect.width * overflowFactor;
        // float yMin = parentRect.yMin - windowRect.height * overflowFactor;
        // float yMax = parentRect.yMax + windowRect.height * overflowFactor;
        float xMin = position.x;
        float yMin = position.y;
        if (xMin < -win.width / 1.5)
        {
            xMin = (float)(-win.width / 1.5);
        }
        else if (xMin > StageUtils.StageWidth - StageUtils.StageWidhtDisparity - win.width / 2.5)
        {
            xMin = (float)(StageUtils.StageWidth - StageUtils.StageWidhtDisparity - win.width / 2.5);
        }
        if (yMin > win.height / 1.5)
        {
            yMin = (float)(win.height / 1.5);
        }
        else if (yMin < -(StageUtils.StageHeight - win.height / 2.5))
        {
            yMin = (float)-(StageUtils.StageHeight - win.height / 2.5);
        }

        // 限制位置
        return new Vector2(
            xMin,
            yMin
        );
    }

    Vector2 LocalToAnchoredPosition(RectTransform parent, RectTransform child, Vector2 localPoint)
    {
        // 获取父对象的大小
        Vector2 parentSize = parent.rect.size;

        // 计算锚点的偏移
        Vector2 anchorOffset = new Vector2(
            (child.anchorMin.x + child.anchorMax.x) / 2f - 0.5f,
            (child.anchorMin.y + child.anchorMax.y) / 2f - 0.5f
        );

        // 转换坐标
        Vector2 anchoredPosition = new Vector2(
            localPoint.x - anchorOffset.x * parentSize.x,
            localPoint.y - anchorOffset.y * parentSize.y
        );

        return anchoredPosition;
    }

    public string winTitle
    {
        get
        {
            return _winTitle;
        }
        set
        {
            _winTitle = value;
            titleText.text = value;
            resizeHanlder();
        }
    }

    public string winName
    {
        get
        {
            return _winName;
        }
        set
        {
            _winName = value;
        }
    }

    private void resizeHanlder()
    {
        // frameBg.width = winW;
        // frameBg.height = winH - 20;
        if (winW > maxTitleW + 20)
        {
            frameTitleBg.width = maxTitleW;
        }
        else
        {
            frameTitleBg.width = minTitleW;
        }

        contentPanel.x = 21;
        contentPanel.y = -41;
        contentPanel.width = winW - contentPanel.x * 2;
        contentPanel.height = winH - 100;
        buttonPanel.x = 50;
        buttonPanel.y = -(winH - 68);
    }

    public void show()
    {
        canvasGroup.blocksRaycasts = true;
        canvasGroup.interactable = true;
        canvasGroup.DOFade(1, winEffectDelay).OnComplete(() => openEffectEnd());
    }

    public void centerWindow()
    {
        win.rectTransform.anchoredPosition = new Vector2((StageUtils.StageWidth - StageUtils.StageWidhtDisparity - win.width) / 2, -(StageUtils.StageHeight - win.height) / 2);
    }

    private void offsetXY()
    {
        x += offsetX;
        y += offsetY;
    }

    private void openEffectEnd()
    {
        if (isModal)
        {
            createModalLayer();
        }
        DOTween.Kill(win);
        OnOpen?.Invoke();
    }

    private void createModalLayer()
    {
        modalLayer.activate = true;
    }

    private void closeHandler()
    {
        close();
    }

    public void close()
    {
        DOTween.Kill(this);
        canvasGroup.DOFade(0, winEffectDelay).OnComplete(() => closeEffectEnd());
    }

    private void closeEffectEnd()
    {
        DOTween.Kill(this);
        OnClose?.Invoke();
        if (viewMediatorName != null && viewMediatorName != "")
        {
            MediatorManager.getInstance().RemoveMediator(viewMediatorName);
        }
        destroy();
        __destroy();
    }
    protected void destroy()
    {
    }

    private void __destroy()
    {
        PopUpWinManager.getInstance().removeWindow(winName);
        AddressableManager.Instance.ReleaseAsset(winXml.prefabPath);
    }

    public void setTitle(string p_value)
    {
        winTitle = p_value;
    }

    public SimpleButton getCloseBtn()
    {
        return closeBtn;
    }

    public ScaleSprite getButtonPanel()
    {
        return buttonPanel;
    }

    public ScaleSprite getContentPanel()
    {
        return contentPanel;
    }

    public void initOffset(int p_x, int p_y)
    {
        offsetX = p_x;
        offsetY = p_y;
    }

    public bool isModal
    {
        get
        {
            return _isModal;
        }
        set
        {
            _isModal = value;
        }
    }

    public bool isCenter
    {
        get
        {
            return _isCenter;
        }
        set
        {
            _isCenter = value;
        }
    }

    public override float width
    {
        get
        {
            return winW;
        }
        set
        {
            if (value < minWinW)
            {
                winW = minWinW;
            }
            else
            {
                winW = value;
            }
            resizeHanlder();
            updateButtonPosition();
        }
    }

    public override float height
    {
        get
        {
            return winH;
        }
        set
        {
            if (value < minWinH)
            {
                winH = minWinH;
            }
            else
            {
                winH = value;
            }
            resizeHanlder();
        }
    }

    // 根据对象名称查找当前对象下的子对象
    // public GameObject getXMLObject(string p_name)
    // {

    // }

    // 根据对象路径查找当前对象下的子对象
    public Transform FindObjectByPath(string path)
    {
        // 直接通过路径查找（示例路径："b/c/x"）
        return transform.Find(path);
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        Debug.Log("PopupWinFrame OnPointerClick");
    }
}