
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Pb;
using UnityEngine;

public class ObjectPropertyProxy
{
    public const int OBJECT_TYPE_BUILDINGS = 1;

    public const int OBJECT_TYPE_CASTLE = 2;

    public const int OBJECT_TYPE_EQUIPMENT = 3;

    public const int OBJECT_TYPE_EXPANDFUNCTION = 4;

    public const int OBJECT_TYPE_HERO = 5;

    public const int OBJECT_TYPE_WORLDNPC = 6;

    public const int OBJECT_TYPE_NPC = 7;

    public const int OBJECT_TYPE_OREMINE = 8;

    public const int OBJECT_TYPE_PLAYER = 9;

    public const int OBJECT_TYPE_WORLDITEMS = 10;

    public const int OBJECT_TYPE_WORLDRESOURCE = 11;

    public const int OBJECT_TYPE_SKILLS = 12;

    public const int OBJECT_TYPE_SOLDIER = 13;

    public const int OBJECT_TYPE_TASK = 14;

    public const int OBJECT_TYPE_NATURAL = 15;

    public const int OBJECT_TYPE_QUESTION = 16;

    public const int OBJECT_TYPE_KINGCASTLE = 17;

    public const int OBJECT_TYPE_BABELTOWER = 18;

    public const int OBJECT_TYPE_BOSS = 19;

    public const int OBJECT_TYPE_OWNEDNATURAL = 20;

    public const int OBJECT_TYPE_TRANSMITDOOR = 21;

    public const int OBJECT_TYPE_RACESKILL = 22;

    public const int OBJECT_TYPE_GUILDSKILL = 23;

    public const int OBJECT_TYPE_KINGCASTLEBOSS = 24;

    public const int OBJECT_TYPE_BATTLE_BUILD = 25;

    public const int OBJECT_TYPE_FIELD_BUILD = 26;

    public const int OBJECT_TYPE_RESISTDRAGON = 27;

    public const int OBJECT_TYPE_GUILDCOPY = 28;

    public const int OBJECT_TYPE_ARMYPORT = 29;

    public const int OBJECT_TYPE_GUILD_BUILD = 30;

    public const int OBJECT_TYPE_PRESENT = 31;

    public const string MSG_OBJECT_UPDATE_BUILDINGS = "MSG_OBJECT_UPDATE_BUILDINGS";

    public const string MSG_OBJECT_UPDATE_CASTLE = "MSG_OBJECT_UPDATE_CASTLE";

    public const string MSG_OBJECT_UPDATE_EQUIPMENT = "MSG_OBJECT_UPDATE_EQUIPMENT";

    public const string MSG_OBJECT_UPDATE_EXPANDFUNCTION = "MSG_OBJECT_UPDATE_EXPANDFUNCTION";

    public const string MSG_OBJECT_UPDATE_HERO = "MSG_OBJECT_UPDATE_HERO";

    public const string MSG_OBJECT_UPDATE_WORLDNPC = "MSG_OBJECT_UPDATE_WORLDNPC";

    public const string MSG_OBJECT_UPDATE_NPC = "MSG_OBJECT_UPDATE_NPC";

    public const string MSG_OBJECT_UPDATE_OREMINE = "MSG_OBJECT_UPDATE_OREMINE";

    public const string MSG_OBJECT_UPDATE_PLAYER = "MSG_OBJECT_UPDATE_PLAYER";

    public const string MSG_OBJECT_UPDATE_WORLDITEMS = "MSG_OBJECT_UPDATE_WORLDITEMS";

    public const string MSG_OBJECT_UPDATE_WORLDRESOURCE = "MSG_OBJECT_UPDATE_WORLDRESOURCE";

    public const string MSG_OBJECT_UPDATE_SKILLS = "MSG_OBJECT_UPDATE_SKILLS";

    public const string MSG_OBJECT_UPDATE_SOLDIER = "MSG_OBJECT_UPDATE_SOLDIER";

    public const string MSG_OBJECT_UPDATE_TASK = "MSG_OBJECT_UPDATE_TASK";

    public const string MSG_OBJECT_UPDATE_QUESTION = "MSG_OBJECT_UPDATE_QUESTION";

    public const string MSG_OBJECT_UPDATE_NATURAL = "MSG_OBJECT_UPDATE_NATURAL";

    public const string MSG_OBJECT_UPDATE_KINGCASTLE = "MSG_OBJECT_UPDATE_KINGCASTLE";

    public const string MSG_OBJECT_UPDATE_BABELTOWER = "MSG_OBJECT_UPDATE_BABELTOWER";

    public const string MSG_OBJECT_UPDATE_BOSS = "MSG_OBJECT_UPDATE_BOSS";

    public const string MSG_OBJECT_UPDATE_OWNEDNATURAL = "MSG_OBJECT_UPDATE_OWNEDNATURAL";

    public const string MSG_OBJECT_UPDATE_TRANSMITDOOR = "MSG_OBJECT_UPDATE_TRANSMITDOOR";

    public const string MSG_OBJECT_UPDATE_RACESKILL = "MSG_OBJECT_UPDATE_RACESKILL";

    public const string MSG_OBJECT_UPDATE_GUILDSKILL = "MSG_OBJECT_UPDATE_GUILDSKILL";

    public const string MSG_OBJECT_UPDATE_PRESENT = "MSG_OBJECT_UPDATE_PRESENT";

    public const string MSG_OBJECT_UPDATE_BATTLE_BUILD = "MSG_OBJECT_UPDATE_BATTLEBUILDING";

    public const string MSG_OBJECT_UPDATE_FIELD_BUILD = "MSG_OBJECT_UPDATE_FIELDBUILDING";

    public const string MSG_OBJECT_UPDATE_GUILDCOPY = "MSG_OBJECT_UPDATE_GUILDCOPY";

    public const string MSG_OBJECT_UPDATE_RESISTDRAGON = "MSG_OBJECT_UPDATE_RESISTDRAGON";

    public const string MSG_OBJECT_UPDATE_ARMYPORT = "MSG_OBJECT_UPDATE_ARMYPORT";

    public const string MSG_OBJECT_UPDATE_GUILDBUILDVO = "MSG_OBJECT_UPDATE_GUILDBUILDVO";

    public const string MSG_CLASS_UPDATE_BUILDINGS = "MSG_CLASS_UPDATE_BUILDINGS";

    public const string MSG_CLASS_UPDATE_CASTLE = "MSG_CLASS_UPDATE_CASTLE";

    public const string MSG_CLASS_UPDATE_EQUIPMENT = "MSG_CLASS_UPDATE_EQUIPMENT";

    public const string MSG_CLASS_UPDATE_EXPANDFUNCTION = "MSG_CLASS_UPDATE_EXPANDFUNCTION";

    public const string MSG_CLASS_UPDATE_HERO = "MSG_CLASS_UPDATE_HERO";

    public const string MSG_CLASS_UPDATE_WORLDNPC = "MSG_CLASS_UPDATE_WORLDNPC";

    public const string MSG_CLASS_UPDATE_NPC = "MSG_CLASS_UPDATE_NPC";

    public const string MSG_CLASS_UPDATE_OREMINE = "MSG_CLASS_UPDATE_OREMINE";

    public const string MSG_CLASS_UPDATE_PLAYER = "MSG_CLASS_UPDATE_PLAYER";

    public const string MSG_CLASS_UPDATE_WORLDITEMS = "MSG_CLASS_UPDATE_WORLDITEMS";

    public const string MSG_CLASS_UPDATE_WORLDRESOURCE = "MSG_CLASS_UPDATE_WORLDRESOURCE";

    public const string MSG_CLASS_UPDATE_SKILLS = "MSG_CLASS_UPDATE_SKILLS";

    public const string MSG_CLASS_UPDATE_SOLDIER = "MSG_CLASS_UPDATE_SOLDIER";

    public const string MSG_CLASS_UPDATE_TASK = "MSG_CLASS_UPDATE_TASK";

    public const string MSG_CLASS_UPDATE_QUESTION = "MSG_CLASS_UPDATE_QUESTION";

    public const string MSG_CLASS_UPDATE_NATURAL = "MSG_CLASS_UPDATE_NATURAL";

    public const string MSG_CLASS_UPDATE_KINGCASTLE = "MSG_CLASS_UPDATE_KINGCASTLE";

    public const string MSG_CLASS_UPDATE_BABELTOWER = "MSG_CLASS_UPDATE_BABELTOWER";

    public const string MSG_CLASS_UPDATE_BOSS = "MSG_CLASS_UPDATE_BOSS";

    public const string MSG_CLASS_UPDATE_OWNEDNATURAL = "MSG_CLASS_UPDATE_OWNEDNATURAL";

    public const string MSG_CLASS_UPDATE_TRANSMITDOOR = "MSG_CLASS_UPDATE_TRANSMITDOOR";

    public const string MSG_CLASS_UPDATE_RACESKILL = "MSG_CLASS_UPDATE_RACESKILL";

    public const string MSG_CLASS_UPDATE_GUILDSKILL = "MSG_CLASS_UPDATE_GUILDSKILL";

    public const string MSG_CLASS_UPDATE_BATTLE_BUILD = "MSG_CLASS_UPDATE_BATTLEBUILDING";

    public const string MSG_CLASS_UPDATE_FIELD_BUILD = "MSG_CLASS_UPDATE_FIELDBUILDING";

    public const string MSG_CLASS_UPDATE_GUILDCOPY = "MSG_CLASS_UPDATE_GUILDCOPY";

    public const string MSG_CLASS_UPDATE_ARMYPORT = "MSG_CLASS_UPDATE_ARMYPORT";

    public const string MSG_CLASS_UPDATE_GUILDBUILDVO = "MSG_CLASS_UPDATE_GUILDBUILDVO";

    public const string GAMEOBJECT_TYPE_OBJECT = "gameObjectType_object";

    public const string GAMEOBJECT_TYPE_CLASS = "gameObjectType_class";

    public const string MSG_CLASS_UPDATE_PRESENT = "MSG_CLASS_UPDATE_PRESENT";

    public const string MSG_CLASS_UPDATE_KINGCASTLEBOSS = "MSG_CLASS_UPDATE_KINGCASTLEBOSS";

    public const string MSG_OBJECT_UPDATE_KINGCASTLEBOSS = "MSG_OBJECT_UPDATE_KINGCASTLEBOSS";

    public const string NAME = "ObjectPropertyProxy";

    private List<paramQueueVO> requestQueueList;

    private readonly Dictionary<int, paramQueueVO> requestQueueDict = new();
    private readonly object dictLock = new object(); // 线程安全锁

    private int requestQueueUid = 0;
    private DataBufferManager dataBuffer;

    public ObjectPropertyProxy()
    {
        dataBuffer = DataBufferManager.getInstance();
        requestQueueList = new();
    }

    public void getObjectProperty(int p_objId, bool p_isUpdate = false, List<int> p_indexs = null, int rid = 0)
    {
        if (!p_isUpdate)
        {
            if (getBufferData(p_objId, GAMEOBJECT_TYPE_OBJECT, requestQueueUid))
            {
                return;
            }
        }
        string p_indexStr = "";
        if (dataBuffer.getData(GAMEOBJECT_TYPE_OBJECT, p_objId) != null && p_indexs != null && p_indexs.IndexOf(-1) == -1)
        {
            p_indexStr = "{" + string.Join(",", p_indexs) + "}";
        }
        Message message = new()
        {
            Action = SocketProxy.CMTS_OBJECT_PROPERTY,
            ObjectPropertyReq = new ObjectPropertyRequest
            {
                PObjId = p_objId,
                PIndexStr = p_indexStr,
                Rid = rid
            }
        };
        WebSocketManager.Instance.SendMessage(message);
    }

    public void getObjectPropertyCallBack(int p_objId, Action<List<GameObjectVO>> p_callbackFun, bool p_isUpdate = false)
    {
        getQueueByIdList(new List<int> { p_objId }, p_callbackFun, GAMEOBJECT_TYPE_OBJECT, p_isUpdate, true);
    }

    public void getClassProperty(int p_classId, bool p_isUpdate = false, int rid = 0)
    {
        if (!p_isUpdate)
        {
            if (getBufferData(p_classId, GAMEOBJECT_TYPE_CLASS, rid))
            {
                return;
            }
        }
        Message message = new()
        {
            Action = SocketProxy.CMTS_CLASS_PROPERTY,
            ClassPropertyReq = new ClassPropertyRequest
            {
                PClassId = p_classId,
                Rid = rid
            }
        };
        WebSocketManager.Instance.SendMessage(message);
    }

    public void getClassPropertyCallBack(int p_classId, Action<List<GameObjectVO>> p_callbackFun, bool p_isUpdate = false)
    {
        getQueueByIdList(new List<int> { p_classId }, p_callbackFun, GAMEOBJECT_TYPE_CLASS, p_isUpdate, true);
    }

    public void getQueueByIdList(List<int> p_list, Action<List<GameObjectVO>> p_callback, string p_questType, bool p_isUpdate = false, bool p_isSingle = false)
    {
        if (p_list == null || p_list.Count == 0 || (p_list.Count == 1 && p_list[0] == 0))
        {
            Debug.LogError("对象ID数组不能为空。");
            return;
        }

        // lock (dictLock)
        // {
        //     foreach (int id in p_list)
        //     {
        //         // 创建新的请求项
        //         var queueItem = new paramQueueVO(
        //             new List<int> { id },
        //             p_callback,
        //             p_isUpdate,
        //             p_questType,
        //             p_isSingle
        //         );
        //         // 按对象ID分组存储
        //         if (!requestQueueDict.ContainsKey(id))
        //         {
        //             requestQueueDict[id] = new List<paramQueueVO>();
        //         }
        //         requestQueueDict[id].Add(queueItem);

        //         if (p_questType == GAMEOBJECT_TYPE_OBJECT)
        //         {
        //             getObjectProperty(id, p_isUpdate);
        //         }
        //         else if (p_questType == GAMEOBJECT_TYPE_CLASS)
        //         {
        //             getClassProperty(id, p_isUpdate);
        //         }
        //     }
        // }

        requestQueueUid++;
        while (requestQueueDict.ContainsKey(requestQueueUid))
        {
            requestQueueUid++;
        }
        requestQueueDict[requestQueueUid] = new paramQueueVO(p_list, p_callback, p_isUpdate, p_questType, p_isSingle);
        // requestQueueList.Add(new paramQueueVO(p_list, p_callback, p_isUpdate, p_questType, p_isSingle));
        foreach (int id in p_list)
        {
            switch (p_questType)
            {
                case GAMEOBJECT_TYPE_OBJECT:
                    getObjectProperty(id, p_isUpdate, null, requestQueueUid);
                    break;
                case GAMEOBJECT_TYPE_CLASS:
                    getClassProperty(id, p_isUpdate, requestQueueUid);
                    break;
                default:
                    break;
            }
        }

    }



    public void formatObjectData(ObjectPropertyResponse p_objData, string p_sendType)
    {
        bool isSuccess = p_objData.IsSuccess;
        int targetId = p_objData.TargetId;
        if (!isSuccess)
        {
            Debug.LogError("接收对象属性错误：对象ID - " + targetId);
            return;
        }
        string paramIndexsStr = null;
        List<int> paramIndexs = null;
        if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
        {
            paramIndexsStr = p_objData.ParamIndexsStr;
            if (paramIndexsStr != "")
            {
                //    paramIndexsStr = paramIndexsStr[1..];
                //    paramIndexsStr = paramIndexsStr[..^1];
                //    paramIndexs = paramIndexsStr.Split(",");
            }
        }

        GameObjectVO obj = null;
        int typeId = p_objData.TypeId;
        int classType = p_objData.ClassType;
        switch (classType)
        {
            case OBJECT_TYPE_PLAYER:
                obj = new PlayerVO(p_objData);
                break;
            case OBJECT_TYPE_HERO:
                obj = new HeroVO(p_objData);
                break;
            case OBJECT_TYPE_CASTLE:
                obj = new CastleVO(p_objData);
                break;
            case OBJECT_TYPE_EQUIPMENT:
                obj = new EquipmentVO(p_objData);
                break;
            default:
                break;
        }
        if (p_sendType == GAMEOBJECT_TYPE_OBJECT && paramIndexs != null && paramIndexs.Count != 0)
        {
            obj.ReadVars(p_objData, paramIndexs);
        }
        if (obj == null)
        {
            Debug.Log("对象数据类型错误");
            return;
        }
        obj.targetId = targetId;
        obj.typeId = typeId;
        obj.classType = classType;
        if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
        {
            dataBuffer.addData(p_sendType, targetId, obj);
        }
        else if (p_sendType == GAMEOBJECT_TYPE_CLASS && targetId <= 65535)
        {
            dataBuffer.addData(p_sendType, typeId, obj);
        }
        else if (p_sendType == GAMEOBJECT_TYPE_CLASS && targetId > 65535)
        {
            dataBuffer.addData(p_sendType, targetId, obj);
        }
        int rid = p_objData.Rid;
        // Debug.Log(targetId + ", rid: " + rid);
        updateData(classType, p_sendType, obj, true, rid);
        // StartCoroutine(UpdateDataCoroutine(classType, p_sendType, obj, true));
    }

    private void updateData(int p_classType, string p_sendType, GameObjectVO p_value, bool p_isNewData, int rid = 0, string p_paramIndexs = null)
    {
        string msgType = getMsgType(p_classType, p_sendType);
        // checkFullRequestQueue(p_value.targetId, p_value);
        checkFullRequestQueue(p_sendType, p_value, p_isNewData, rid);

        MessageBus.Publish(msgType, new List<object> { p_value, p_paramIndexs });
    }

    // private IEnumerator UpdateDataCoroutine(int p_classType, string p_sendType, GameObjectVO p_value, bool p_isNewData, string p_paramIndexs = null)
    // {
    //     lock (dictLock)
    //     {
    //         string msgType = getMsgType(p_classType, p_sendType);
    //         checkFullRequestQueue(p_value.targetId, p_value);
    //         MessageBus.Publish(msgType, new List<object> { p_value, p_paramIndexs });
    //         yield return null;
    //     }
    // }

    // private void checkFullRequestQueue(int resolvedId, GameObjectVO resolvedData)
    // {
    //     if (requestQueueDict.TryGetValue(resolvedId, out var requestList))
    //     {
    //         List<paramQueueVO> completedRequests = new();

    //         // 遍历所有关联的请求
    //         foreach (var request in requestList)
    //         {
    //             // 更新数据并检查是否完成
    //             int index = request.list.IndexOf(resolvedId);
    //             if (index != -1)
    //             {
    //                 request.objectList[index] = resolvedData;
    //                 if (request.IsComplete())
    //                 {
    //                     completedRequests.Add(request);
    //                     // 触发回调
    //                     request.callbackFunction?.Invoke(request.objectList);
    //                 }
    //             }
    //         }
    //         // 清理已完成的请求
    //         foreach (var completed in completedRequests)
    //         {
    //             requestList.Remove(completed);
    //         }
    //         // 如果该ID下无剩余请求，移除键
    //         if (requestList.Count == 0)
    //         {
    //             requestQueueDict.Remove(resolvedId);
    //         }
    //     }
    // }
    private void checkFullRequestQueue(string p_sendType, GameObjectVO p_value, bool p_isNewData, int rid)
    {
        if (requestQueueDict.TryGetValue(rid, out var data))
        {
            if (!(data.isNewData == true && p_isNewData == false))
            {
                bool updated = false;
                // 遍历所有关联的请求，更新所有匹配的targetId
                for (int i = 0; i < data.list.Count; i++)
                {
                    if (data.list[i] == p_value.targetId)
                    {
                        data.objectList[i] = p_value;
                        updated = true;
                    }
                }

                // 只有在实际更新了数据后才检查是否完成
                if (updated && data.IsComplete())
                {
                    // 触发回调
                    data.callbackFunction?.Invoke(data.objectList);
                    // 清理已完成的请求
                    requestQueueDict.Remove(rid);
                }
            }
        }
    }

    // private void checkFullRequestQueue(string p_sendType, GameObjectVO p_value, bool p_isNewData)
    // {
    //     int currentId;
    //     int currentFullIndex = -1;
    //     paramQueueVO currentFullObj = null;
    //     int currentObjId = 0;
    //     bool isFull = false;
    //     bool isFindIndex = true;

    //     // 查找并更新队列项
    //     // Debug.Log("检查请求队列requestQueueList: " + requestQueueList.Count);
    //     for (int i = 0; i < requestQueueList.Count; i++)
    //     {
    //         paramQueueVO data = requestQueueList[i];
    //         // Debug.Log("检查请求队列项: " + DebugUtils.ObjectToString(data));
    //         // Debug.Log("检查请求队列项1, isNewData: " + data.isNewData + "objectList: " + data.objectList.ToString() + "callback: " + data.callbackFunction);

    //         if (!(data.isNewData == true && p_isNewData == false))
    //         {
    //             // Debug.Log("检查请求队列项, isNewData: " + data.isNewData + "objectList: " + data.objectList + "callback: " + data.callbackFunction + ", data.list.Count: " + data.list.Count);
    //             isFull = true;
    //             for (int j = 0; j < data.list.Count; j++)
    //             {
    //                 currentId = data.list[j];
    //                 // Debug.Log("检查请求队列项2, currentId: " + currentId + "p_sendType: " + p_sendType);
    //                 if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
    //                 {
    //                     currentObjId = p_value.targetId;
    //                 }
    //                 else if (p_sendType == GAMEOBJECT_TYPE_CLASS && currentId <= 65535)
    //                 {
    //                     if (p_value.classType == OBJECT_TYPE_EQUIPMENT)
    //                     {
    //                         if (((EquipmentVO)p_value).plusValue.Count == 0 && ((EquipmentVO)p_value).forging.Count == 0)
    //                         {
    //                             currentObjId = p_value.typeId;
    //                         }
    //                     }
    //                     else
    //                     {
    //                         currentObjId = p_value.typeId;
    //                     }
    //                 }
    //                 else if (p_sendType == GAMEOBJECT_TYPE_CLASS && currentId > 65535)
    //                 {
    //                     currentObjId = p_value.targetId;
    //                     // Debug.Log("检查请求队列项, currentObj: " + data.objectList[j]);
    //                 }
    //                 if (currentId == currentObjId && data.objectList[j] == null && data.sendType == p_sendType)
    //                 {
    //                     data.objectList[j] = p_value;
    //                     isFindIndex = true;
    //                 }
    //                 if (data.objectList[j] == null)
    //                 {
    //                     isFull = false;
    //                 }
    //             }
    //             if (isFull && isFindIndex)
    //             {
    //                 currentFullIndex = i;
    //                 currentFullObj = data;
    //             }
    //             else if (isFull && !isFindIndex)
    //             {
    //                 Debug.LogError("意料之外错误！请检查程序逻辑");
    //             }
    //             if (isFindIndex)
    //             {
    //                 break;
    //             }
    //         }
    //     }
    //     if (isFull)
    //     {
    //         requestQueueList.RemoveAt(currentFullIndex);
    //         currentFullObj.callbackFunction?.Invoke(currentFullObj.objectList);
    //     }
    // }
    private string getMsgType(int p_classType, string p_sendType)
    {
        string msgType = null;
        switch (p_classType)
        {
            case OBJECT_TYPE_BUILDINGS:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_BUILDINGS;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_BUILDINGS;
                }
                break;
            case OBJECT_TYPE_CASTLE:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_CASTLE;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_CASTLE;
                }
                break;
            case OBJECT_TYPE_EQUIPMENT:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_EQUIPMENT;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_EQUIPMENT;
                }
                break;
            case OBJECT_TYPE_EXPANDFUNCTION:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_EXPANDFUNCTION;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_EXPANDFUNCTION;
                }
                break;
            case OBJECT_TYPE_HERO:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_HERO;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_HERO;
                }
                break;
            case OBJECT_TYPE_WORLDNPC:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_WORLDNPC;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_WORLDNPC;
                }
                break;
            case OBJECT_TYPE_NPC:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_NPC;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_NPC;
                }
                break;
            case OBJECT_TYPE_OREMINE:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_OREMINE;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_OREMINE;
                }
                break;
            case OBJECT_TYPE_PLAYER:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_PLAYER;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_PLAYER;
                }
                break;
            case OBJECT_TYPE_WORLDITEMS:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_WORLDITEMS;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_WORLDITEMS;
                }
                break;
            case OBJECT_TYPE_WORLDRESOURCE:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_WORLDRESOURCE;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_WORLDRESOURCE;
                }
                break;
            case OBJECT_TYPE_SKILLS:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_SKILLS;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_SKILLS;
                }
                break;
            case OBJECT_TYPE_SOLDIER:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_SOLDIER;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_SOLDIER;
                }
                break;
            case OBJECT_TYPE_TASK:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_TASK;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_TASK;
                }
                break;
            case OBJECT_TYPE_QUESTION:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_QUESTION;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_QUESTION;
                }
                break;
            case OBJECT_TYPE_NATURAL:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_NATURAL;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_NATURAL;
                }
                break;
            case OBJECT_TYPE_KINGCASTLE:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_KINGCASTLE;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_KINGCASTLE;
                }
                break;
            case OBJECT_TYPE_BABELTOWER:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_BABELTOWER;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_BABELTOWER;
                }
                break;
            case OBJECT_TYPE_BOSS:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_BOSS;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_BOSS;
                }
                break;
            case OBJECT_TYPE_OWNEDNATURAL:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_OWNEDNATURAL;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_OWNEDNATURAL;
                }
                break;
            case OBJECT_TYPE_TRANSMITDOOR:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_TRANSMITDOOR;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_TRANSMITDOOR;
                }
                break;
            case OBJECT_TYPE_RACESKILL:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_RACESKILL;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_RACESKILL;
                }
                break;
            case OBJECT_TYPE_GUILDSKILL:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_GUILDSKILL;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_GUILDSKILL;
                }
                break;
            case OBJECT_TYPE_BATTLE_BUILD:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_BATTLE_BUILD;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_BATTLE_BUILD;
                }
                break;
            case OBJECT_TYPE_FIELD_BUILD:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_FIELD_BUILD;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_FIELD_BUILD;
                }
                break;
            case OBJECT_TYPE_GUILDCOPY:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_GUILDCOPY;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_GUILDCOPY;
                }
                break;
            case OBJECT_TYPE_RESISTDRAGON:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_RESISTDRAGON;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_OBJECT_UPDATE_RESISTDRAGON;
                }
                break;
            case OBJECT_TYPE_ARMYPORT:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_ARMYPORT;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_ARMYPORT;
                }
                break;
            case OBJECT_TYPE_PRESENT:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_PRESENT;
                }
                else if (p_sendType == GAMEOBJECT_TYPE_CLASS)
                {
                    msgType = MSG_CLASS_UPDATE_PRESENT;
                }
                break;
            case OBJECT_TYPE_KINGCASTLEBOSS:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_KINGCASTLEBOSS;
                }
                else
                {
                    msgType = MSG_CLASS_UPDATE_KINGCASTLEBOSS;
                }
                break;
            case OBJECT_TYPE_GUILD_BUILD:
                if (p_sendType == GAMEOBJECT_TYPE_OBJECT)
                {
                    msgType = MSG_OBJECT_UPDATE_GUILDBUILDVO;
                }
                else
                {
                    msgType = MSG_CLASS_UPDATE_GUILDBUILDVO;
                }
                break;
            default:
                Debug.Log("未知对象类型");
                break;
        }
        return msgType;
    }

    private bool getBufferData(int p_Id, string p_type, int rid = 0)
    {
        GameObjectVO bd = null;
        bool isExistData = dataBuffer.isExistData(p_type, p_Id);
        if (isExistData)
        {
            bd = dataBuffer.getData(p_type, p_Id) as GameObjectVO;
            updateData(bd.classType, p_type, bd, false, rid);
            return true;
        }
        return false;
    }

    public GameObjectVO GetObjectData(int p_targetId)
    {
        return dataBuffer.getData(GAMEOBJECT_TYPE_OBJECT, p_targetId) as GameObjectVO;
    }

    public GameObjectVO GetClassData(int p_targetId)
    {
        return dataBuffer.getData(GAMEOBJECT_TYPE_CLASS, p_targetId) as GameObjectVO;
    }

    public bool IsExistObjectData(int p_targetId)
    {
        return dataBuffer.isExistData(GAMEOBJECT_TYPE_OBJECT, p_targetId);
    }

    public bool IsExistClassData(int p_targetId)
    {
        return dataBuffer.isExistData(GAMEOBJECT_TYPE_CLASS, p_targetId);
    }

    public void RemoveObjectData(int p_targetId)
    {
        dataBuffer.delData(GAMEOBJECT_TYPE_OBJECT, p_targetId);
    }

    public void RemoveClassData(int p_targetId)
    {
        dataBuffer.delData(GAMEOBJECT_TYPE_CLASS, p_targetId);
    }
}


public class paramQueueVO
{
    public List<int> list;
    public Action<List<GameObjectVO>> callbackFunction;
    public List<GameObjectVO> objectList;

    public bool isNewData;
    public bool isSingleMode;
    public string sendType;

    public paramQueueVO(List<int> p_list, Action<List<GameObjectVO>> callback, bool p_isNewData, string p_sendType, bool p_isSingle = false)
    {
        list = p_list;
        callbackFunction = callback;
        isNewData = p_isNewData;
        isSingleMode = p_isSingle;
        sendType = p_sendType;
        objectList = new List<GameObjectVO>(new GameObjectVO[p_list.Count]);
    }

    public void AddResult(int index, GameObjectVO result)
    {
        objectList[index] = result;
    }

    public bool IsComplete()
    {
        return objectList.All(result => result != null);
    }
}

