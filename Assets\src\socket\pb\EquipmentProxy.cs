// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Assets/src/socket/protobuf/proxy/EquipmentProxy.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Pb {

  /// <summary>Holder for reflection information generated from Assets/src/socket/protobuf/proxy/EquipmentProxy.proto</summary>
  public static partial class EquipmentProxyReflection {

    #region Descriptor
    /// <summary>File descriptor for Assets/src/socket/protobuf/proxy/EquipmentProxy.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EquipmentProxyReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CjVBc3NldHMvc3JjL3NvY2tldC9wcm90b2J1Zi9wcm94eS9FcXVpcG1lbnRQ",
            "cm94eS5wcm90bxICcGIiSwoWSEVST19FUVVJUE1FTlRfUmVxdWVzdBIOCgZo",
            "ZXJvSWQYASABKAUSEwoLZXF1aXBtZW50SWQYAiABKAUSDAoEcGFydBgDIAEo",
            "BSI7ChdIRVJPX0VRVUlQTUVOVF9SZXNwb25zZRIQCghoYXZlRGF0YRgBIAEo",
            "BRIOCgZoZXJvSWQYAiABKAUiMQoPSEVST0pOX1Jlc3BvbnNlEgwKBGlzT2sY",
            "ASABKAUSEAoIcmVzaWR1YWwYAiADKAVCBVoDL3BiYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Pb.HERO_EQUIPMENT_Request), global::Pb.HERO_EQUIPMENT_Request.Parser, new[]{ "HeroId", "EquipmentId", "Part" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Pb.HERO_EQUIPMENT_Response), global::Pb.HERO_EQUIPMENT_Response.Parser, new[]{ "HaveData", "HeroId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Pb.HEROJN_Response), global::Pb.HEROJN_Response.Parser, new[]{ "IsOk", "Residual" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class HERO_EQUIPMENT_Request : pb::IMessage<HERO_EQUIPMENT_Request>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HERO_EQUIPMENT_Request> _parser = new pb::MessageParser<HERO_EQUIPMENT_Request>(() => new HERO_EQUIPMENT_Request());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<HERO_EQUIPMENT_Request> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Pb.EquipmentProxyReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HERO_EQUIPMENT_Request() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HERO_EQUIPMENT_Request(HERO_EQUIPMENT_Request other) : this() {
      heroId_ = other.heroId_;
      equipmentId_ = other.equipmentId_;
      part_ = other.part_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HERO_EQUIPMENT_Request Clone() {
      return new HERO_EQUIPMENT_Request(this);
    }

    /// <summary>Field number for the "heroId" field.</summary>
    public const int HeroIdFieldNumber = 1;
    private int heroId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int HeroId {
      get { return heroId_; }
      set {
        heroId_ = value;
      }
    }

    /// <summary>Field number for the "equipmentId" field.</summary>
    public const int EquipmentIdFieldNumber = 2;
    private int equipmentId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int EquipmentId {
      get { return equipmentId_; }
      set {
        equipmentId_ = value;
      }
    }

    /// <summary>Field number for the "part" field.</summary>
    public const int PartFieldNumber = 3;
    private int part_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Part {
      get { return part_; }
      set {
        part_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as HERO_EQUIPMENT_Request);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(HERO_EQUIPMENT_Request other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (HeroId != other.HeroId) return false;
      if (EquipmentId != other.EquipmentId) return false;
      if (Part != other.Part) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HeroId != 0) hash ^= HeroId.GetHashCode();
      if (EquipmentId != 0) hash ^= EquipmentId.GetHashCode();
      if (Part != 0) hash ^= Part.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HeroId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(HeroId);
      }
      if (EquipmentId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(EquipmentId);
      }
      if (Part != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Part);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HeroId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(HeroId);
      }
      if (EquipmentId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(EquipmentId);
      }
      if (Part != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Part);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HeroId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HeroId);
      }
      if (EquipmentId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(EquipmentId);
      }
      if (Part != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Part);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(HERO_EQUIPMENT_Request other) {
      if (other == null) {
        return;
      }
      if (other.HeroId != 0) {
        HeroId = other.HeroId;
      }
      if (other.EquipmentId != 0) {
        EquipmentId = other.EquipmentId;
      }
      if (other.Part != 0) {
        Part = other.Part;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            HeroId = input.ReadInt32();
            break;
          }
          case 16: {
            EquipmentId = input.ReadInt32();
            break;
          }
          case 24: {
            Part = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            HeroId = input.ReadInt32();
            break;
          }
          case 16: {
            EquipmentId = input.ReadInt32();
            break;
          }
          case 24: {
            Part = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class HERO_EQUIPMENT_Response : pb::IMessage<HERO_EQUIPMENT_Response>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HERO_EQUIPMENT_Response> _parser = new pb::MessageParser<HERO_EQUIPMENT_Response>(() => new HERO_EQUIPMENT_Response());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<HERO_EQUIPMENT_Response> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Pb.EquipmentProxyReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HERO_EQUIPMENT_Response() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HERO_EQUIPMENT_Response(HERO_EQUIPMENT_Response other) : this() {
      haveData_ = other.haveData_;
      heroId_ = other.heroId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HERO_EQUIPMENT_Response Clone() {
      return new HERO_EQUIPMENT_Response(this);
    }

    /// <summary>Field number for the "haveData" field.</summary>
    public const int HaveDataFieldNumber = 1;
    private int haveData_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int HaveData {
      get { return haveData_; }
      set {
        haveData_ = value;
      }
    }

    /// <summary>Field number for the "heroId" field.</summary>
    public const int HeroIdFieldNumber = 2;
    private int heroId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int HeroId {
      get { return heroId_; }
      set {
        heroId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as HERO_EQUIPMENT_Response);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(HERO_EQUIPMENT_Response other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (HaveData != other.HaveData) return false;
      if (HeroId != other.HeroId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HaveData != 0) hash ^= HaveData.GetHashCode();
      if (HeroId != 0) hash ^= HeroId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HaveData != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(HaveData);
      }
      if (HeroId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(HeroId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HaveData != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(HaveData);
      }
      if (HeroId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(HeroId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HaveData != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HaveData);
      }
      if (HeroId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HeroId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(HERO_EQUIPMENT_Response other) {
      if (other == null) {
        return;
      }
      if (other.HaveData != 0) {
        HaveData = other.HaveData;
      }
      if (other.HeroId != 0) {
        HeroId = other.HeroId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            HaveData = input.ReadInt32();
            break;
          }
          case 16: {
            HeroId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            HaveData = input.ReadInt32();
            break;
          }
          case 16: {
            HeroId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class HEROJN_Response : pb::IMessage<HEROJN_Response>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HEROJN_Response> _parser = new pb::MessageParser<HEROJN_Response>(() => new HEROJN_Response());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<HEROJN_Response> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Pb.EquipmentProxyReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HEROJN_Response() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HEROJN_Response(HEROJN_Response other) : this() {
      isOk_ = other.isOk_;
      residual_ = other.residual_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HEROJN_Response Clone() {
      return new HEROJN_Response(this);
    }

    /// <summary>Field number for the "isOk" field.</summary>
    public const int IsOkFieldNumber = 1;
    private int isOk_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IsOk {
      get { return isOk_; }
      set {
        isOk_ = value;
      }
    }

    /// <summary>Field number for the "residual" field.</summary>
    public const int ResidualFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_residual_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> residual_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> Residual {
      get { return residual_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as HEROJN_Response);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(HEROJN_Response other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (IsOk != other.IsOk) return false;
      if(!residual_.Equals(other.residual_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (IsOk != 0) hash ^= IsOk.GetHashCode();
      hash ^= residual_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (IsOk != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(IsOk);
      }
      residual_.WriteTo(output, _repeated_residual_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (IsOk != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(IsOk);
      }
      residual_.WriteTo(ref output, _repeated_residual_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (IsOk != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IsOk);
      }
      size += residual_.CalculateSize(_repeated_residual_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(HEROJN_Response other) {
      if (other == null) {
        return;
      }
      if (other.IsOk != 0) {
        IsOk = other.IsOk;
      }
      residual_.Add(other.residual_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            IsOk = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            residual_.AddEntriesFrom(input, _repeated_residual_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            IsOk = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            residual_.AddEntriesFrom(ref input, _repeated_residual_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
