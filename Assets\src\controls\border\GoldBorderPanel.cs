using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI; // Add this using directive

public class GoldBorderPanel : ScaleSprite, IPointerEnterHandler, IPointerExitHandler, IPointerClickHandler, IPointerDownHandler, IPointerUpHandler
{

    public Texture2D handCursorTexture;
    public float borderWidth;
    public float borderHeight;

    public CommonImageLoader loader;

    private bool isPointerOver;
    private bool isPressed;

    public bool useHandCursor = true;

    public bool buttonMode = true;

    public UnityEvent<GoldBorderPanel> OnClick = new();


    public void SetBorderSize(float width, float height)
    {
        borderWidth = width;
        borderHeight = height;
        rectTransform.sizeDelta = new Vector2(width, height);
    }

    // public void SetImage(Sprite sprite)
    // {
    //     imageLoader.sprite = sprite;
    //     // 激活image组件
    //     imageLoader.enabled = true;
    // }


    public virtual void OnPointerClick(PointerEventData eventData)
    {
        Debug.Log("GoldBorderPanel OnPointerClick");
        if (!buttonMode)
        {
            return;
        }
        OnClick?.Invoke(this);
    }

    public virtual void OnPointerEnter(PointerEventData eventData)
    {
        if (!buttonMode)
        {
            return;
        }
        isPointerOver = true;
        SetHandCursor(true);
    }

    public virtual void OnPointerExit(PointerEventData eventData)
    {
        if (!buttonMode)
        {
            return;
        }
        isPointerOver = false;
        if (!isPressed)
        {
            SetHandCursor(false);
        }
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        if (!buttonMode)
        {
            return;
        }
        isPressed = true;
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        if (!buttonMode)
        {
            return;
        }
        isPressed = false;
        if (!isPointerOver)
        {
            SetHandCursor(false);
        }
    }

    private void SetHandCursor(bool isHand)
    {
        if (useHandCursor)
        {
            if (isHand)
            {
                CursorManager.Instance.SetPointCursor();
            }
            else
            {
                CursorManager.Instance.SetDefaultCursor();
            }
        }

    }
}