Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.3f1 (f34db9734971) revision 15945145'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 65299 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-28T09:41:20Z

COMMAND LINE ARGUMENTS:
D:\software\unity3d\6000.1.3f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/unity/HeroCastleRemake-Unity6.1
-logFile
Logs/AssetImportWorker1.log
-srvPort
9112
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/unity/HeroCastleRemake-Unity6.1
D:/unity/HeroCastleRemake-Unity6.1
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [17476]  Target information:

Player connection [17476]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1094034930 [EditorId] 1094034930 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17476]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1094034930 [EditorId] 1094034930 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17476]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1094034930 [EditorId] 1094034930 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17476]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1094034930 [EditorId] 1094034930 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17476]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1094034930 [EditorId] 1094034930 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17476]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1094034930 [EditorId] 1094034930 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17476] Host joined multi-casting on [***********:54997]...
Player connection [17476] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.3f1 (f34db9734971)
[Subsystems] Discovering subsystems at path D:/software/unity3d/6000.1.3f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/unity/HeroCastleRemake-Unity6.1/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'D:/software/unity3d/6000.1.3f1/Editor/Data/Managed'
Mono path[1] = 'D:/software/unity3d/6000.1.3f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/software/unity3d/6000.1.3f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56580
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.006502 seconds.
- Loaded All Assemblies, in  0.343 seconds
Native extension for LinuxStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for AppleTV target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2454 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.818 seconds
Domain Reload Profiling: 3156ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (143ms)
		LoadAssemblies (108ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (138ms)
			TypeCache.Refresh (136ms)
				TypeCache.ScanAssembly (122ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2818ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2774ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2581ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (96ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.891 seconds
Refreshing native plugins compatible for Editor in 3.02 ms, found 4 plugins.
Native extension for LinuxStandalone target not found
Native extension for AppleTV target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
System.Exception: Error! Visual Scripting: couldn't find visual scripting package
  at Unity.VisualScripting.PackageVersionUtility.get_version () [0x00043] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\PackageVersionUtility.cs:31 
  at Unity.VisualScripting.BoltCoreManifest.get_version () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugin\BoltCoreManifest.cs:11 
  at Unity.VisualScripting.PluginManifest.get_currentVersion () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginManifest.cs:33 
  at Unity.VisualScripting.PluginManifest.get_versionMismatch () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginManifest.cs:48 
  at Unity.VisualScripting.PluginContainer.Initialize () [0x00525] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginContainer.cs:208 
  at Unity.VisualScripting.PluginContainer.InitializeOnLoad () [0x00009] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginContainer.cs:32 
  at Unity.VisualScripting.VSUsageUtility.DoInitializeOnLoadCalls () [0x00034] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:76 
  at Unity.VisualScripting.VSUsageUtility.set_isVisualScriptingUsed (System.Boolean value) [0x0002c] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:114 
  at Unity.VisualScripting.VSUsageUtility.CheckAndSetIsVisualScriptingUsed () [0x00032] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:48 
  at Unity.VisualScripting.VSUsageUtility..cctor () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:22 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.029 seconds
Domain Reload Profiling: 1905ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (656ms)
		LoadAssemblies (456ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (206ms)
				TypeCache.ScanAssembly (189ms)
			BuildScriptInfoCaches (59ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1030ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (927ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (719ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 287 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8638 unused Assets / (10.0 MB). Loaded Objects now: 9292.
Memory consumption went from 227.4 MB to 217.4 MB.
Total: 17.864900 ms (FindLiveObjects: 0.562400 ms CreateObjectMapping: 0.361500 ms MarkObjects: 13.746000 ms  DeleteObjects: 3.193900 ms)

========================================================================
Received Import Request.
  Time since last request: 22390.608720 seconds.
  path: Assets/Game/Prefabs/popupwin/heroInfo/HeroLookWin.prefab
  artifactKey: Guid(a9d994ef07d19a84fbac3cc65714a4be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Game/Prefabs/popupwin/heroInfo/HeroLookWin.prefab using Guid(a9d994ef07d19a84fbac3cc65714a4be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Key 'TIP_HE_LEVEL_10_REEL' not found in file 'scene'
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
LanguageManager:GetText (string,string,object[]) (at Assets/src/manager/LanguageManager.cs:139)
LanguageManager:getScene (string,object[]) (at Assets/src/manager/LanguageManager.cs:102)
HeroSkillView:.cctor () (at Assets/src/mediator/popupwin/heroInfo/HeroSkillView.cs:14)

(Filename: Assets/src/manager/LanguageManager.cs Line: 139)

Key 'TIP_HE_LEVEL_20_REEL' not found in file 'scene'
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
LanguageManager:GetText (string,string,object[]) (at Assets/src/manager/LanguageManager.cs:139)
LanguageManager:getScene (string,object[]) (at Assets/src/manager/LanguageManager.cs:102)
HeroSkillView:.cctor () (at Assets/src/mediator/popupwin/heroInfo/HeroSkillView.cs:14)

(Filename: Assets/src/manager/LanguageManager.cs Line: 139)

Key 'TIP_HE_LEVEL_30_REEL' not found in file 'scene'
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
LanguageManager:GetText (string,string,object[]) (at Assets/src/manager/LanguageManager.cs:139)
LanguageManager:getScene (string,object[]) (at Assets/src/manager/LanguageManager.cs:102)
HeroSkillView:.cctor () (at Assets/src/mediator/popupwin/heroInfo/HeroSkillView.cs:14)

(Filename: Assets/src/manager/LanguageManager.cs Line: 139)

Key 'TIP_HE_LEVEL_40_REEL' not found in file 'scene'
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
LanguageManager:GetText (string,string,object[]) (at Assets/src/manager/LanguageManager.cs:139)
LanguageManager:getScene (string,object[]) (at Assets/src/manager/LanguageManager.cs:102)
HeroSkillView:.cctor () (at Assets/src/mediator/popupwin/heroInfo/HeroSkillView.cs:14)

(Filename: Assets/src/manager/LanguageManager.cs Line: 139)

 -> (artifact id: 'a2de0c2ee00133ade3c661df6c123f7b') in 0.6104039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10602

========================================================================
Received Import Request.
  Time since last request: 586.500914 seconds.
  path: Assets/Art/Textures/UI/equipment/36001_M.png
  artifactKey: Guid(5728c928acd2e4f428cb04f641a1cc1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36001_M.png using Guid(5728c928acd2e4f428cb04f641a1cc1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '401c83718c9e785d5b5dab11f82981bf') in 0.9166945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.023781 seconds.
  path: Assets/Art/Textures/UI/equipment/36005_M.png
  artifactKey: Guid(04010b5ee2f761e4791f40a690750099) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36005_M.png using Guid(04010b5ee2f761e4791f40a690750099) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '322bb5d12c82aa977e6f1bbf7e60cbef') in 0.8253049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Art/Textures/UI/equipment/36008_M.png
  artifactKey: Guid(abfbddf2f0e3c664e87c1a9e42a93e2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36008_M.png using Guid(abfbddf2f0e3c664e87c1a9e42a93e2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '09207c4903cb6e126b03aed9d22c2c6f') in 0.8846423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Art/Textures/UI/equipment/36020_M.png
  artifactKey: Guid(06920c4db3b3a9c44803b8539021f8af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36020_M.png using Guid(06920c4db3b3a9c44803b8539021f8af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1002785c7dbe53d090c4b3856733e798') in 0.9127205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Art/Textures/UI/equipment/36033_M.png
  artifactKey: Guid(8a6b69e0daca3a840b24d258d5366f9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36033_M.png using Guid(8a6b69e0daca3a840b24d258d5366f9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '361269e876b08d573d25a7ba41bde123') in 0.8466079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Art/Textures/UI/equipment/36028_M.png
  artifactKey: Guid(71f2ed09de0f9ba4882130b05a74e854) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36028_M.png using Guid(71f2ed09de0f9ba4882130b05a74e854) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b7ea11ba6b7c7ac606f990c064d7b62') in 0.783265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Art/Textures/UI/equipment/36017_M.png
  artifactKey: Guid(779f72cd8902f6342b640ef64d30ace6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36017_M.png using Guid(779f72cd8902f6342b640ef64d30ace6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5a528a599f94ead57c7e6ec4ad6ebe3') in 0.783993 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Art/Textures/UI/equipment/36009_M.png
  artifactKey: Guid(9c8d7b58de4d72849ae78045ebb39508) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36009_M.png using Guid(9c8d7b58de4d72849ae78045ebb39508) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f92fe1d398829f832dc999e53c8fda45') in 0.7821792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Art/Textures/UI/equipment/36022_M.png
  artifactKey: Guid(5c19d072905032448b418c94511d5ba0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36022_M.png using Guid(5c19d072905032448b418c94511d5ba0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd381f756f7603d39095dc9b050f8392') in 0.8287757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Art/Textures/UI/equipment/36014_M.png
  artifactKey: Guid(02805a5f22d62ec40b31c35d64435944) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36014_M.png using Guid(02805a5f22d62ec40b31c35d64435944) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9d9ae1f6d296d1fcaa7e6af7e63affd') in 0.8965361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Art/Textures/UI/equipment/36025_M.png
  artifactKey: Guid(d6b79f866d97256478d516e5f425eda4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36025_M.png using Guid(d6b79f866d97256478d516e5f425eda4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c2bc7875b1be40491200b4f241f2b68b') in 1.0248553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 2.924789 seconds.
  path: Assets/Art/Textures/UI/equipment/36003_M.png
  artifactKey: Guid(53affdb96aaa8ef48b0765c7fd755f07) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36003_M.png using Guid(53affdb96aaa8ef48b0765c7fd755f07) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bcf45d20bccf6c3b6b0573c903ab81b8') in 1.4046818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Art/Textures/UI/equipment/36011_M.png
  artifactKey: Guid(62dc4236b12b9094ba1867e27523eff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36011_M.png using Guid(62dc4236b12b9094ba1867e27523eff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37e9cb43a5a3d1ffbf5955cce12230af') in 1.3042444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Art/Textures/UI/equipment/36022_M.png
  artifactKey: Guid(5c19d072905032448b418c94511d5ba0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36022_M.png using Guid(5c19d072905032448b418c94511d5ba0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '690002894003fccee3bb0ccf17b76066') in 1.285004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Art/Textures/UI/equipment/36012_M.png
  artifactKey: Guid(b81a163cfe108e74a8fe70dbb358d1b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36012_M.png using Guid(b81a163cfe108e74a8fe70dbb358d1b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '39baf50d3673c9449bc0b0a051e30877') in 1.3410817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Art/Textures/UI/equipment/36036_M.png
  artifactKey: Guid(0a5a60c8a254eb94eac7b92c2ad4e305) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36036_M.png using Guid(0a5a60c8a254eb94eac7b92c2ad4e305) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f095f6e964e3a0b1d30ed71932e7de71') in 1.062058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0