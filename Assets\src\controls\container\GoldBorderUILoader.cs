

using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

public class GoldBorderUILoader : GoldBorderPanel
{
    public UnityEvent<GoldBorderUILoader> OnClick_UI = new();

    public CommonTextMeshPro txt;
    protected override void Awake()
    {
        base.Awake();
    }

    public CommonImageLoader getLoader()
    {
        return loader;
    }

    public override void OnPointerClick(PointerEventData eventData)
    {
        Debug.Log("GoldBorderUILoader OnPointerClick");
        if (!buttonMode)
        {
            return;
        }
        OnClick_UI?.Invoke(this);
    }

    public Sprite source
    {
        get => loader.source;
        set
        {
            loader.source = value;
        }
    }
}