
using System;
using System.Collections.Generic;
using UnityEngine;

public class GoodsInfoBindAsync
{

    private static readonly Dictionary<int, int> itemsTime_list = new();

    private static Dictionary<string, Dictionary<int, EquipmentVO>> goodsSceneMap = new();

    private GoodsInfoBindAsync()
    {
    }

    public static void showTipHandler(GameObject obj, int id, string sceneName)
    {
        if (obj == null)
        {
            Debug.LogError("GoodsInfoBindAsync: showTipHandler obj is null");
            return;
        }
        if (goodsSceneMap.ContainsKey(sceneName))
        {
            if (goodsSceneMap[sceneName].ContainsKey(id))
            {
                getTipVO(goodsSceneMap[sceneName][id], obj);
                return;
            }
        }
        else
        {
            goodsSceneMap.Add(sceneName, new());
        }
        if (GoodsProxy.goodInfo_map.ContainsKey(id))
        {
            GameObjectVO temp_vo = GoodsProxy.goodInfo_map[id];
            if (temp_vo != null)
            {
                getTipVO(temp_vo, obj);
            }
        }
        else
        {
            ProxyManager.Instance.objectPropertyProxy.getClassPropertyCallBack(id, vo_list =>
            {
                if (vo_list.Count > 0)
                {
                    if (vo_list[0] is EquipmentVO)
                    {
                        if (!goodsSceneMap[sceneName].ContainsKey(vo_list[0].targetId))
                        {
                            goodsSceneMap[sceneName].Add(vo_list[0].targetId, vo_list[0] as EquipmentVO);
                        }
                    }
                    getTipVO(vo_list[0], obj);
                }
            }, true);
        }
    }

    private static void getTipVO(GameObjectVO temp_vo, GameObject obj)
    {
        string tipStr = null;
        if (temp_vo is EquipmentVO)
        {
            EquipmentVO equipmentVO = temp_vo as EquipmentVO;

            if (equipmentVO.part != 20)
            {
                Debug.Log("temp_vo: " + equipmentVO.name + " part: " + equipmentVO.part);
                Debug.Log("tip: " + getToolTipContent(equipmentVO));
                ToolTipManager.getInstance().setToolTip(obj, getToolTipContent(equipmentVO));
                return;
            }
            tipStr = StringUtils.handleTip(getGoodsToolTip(temp_vo.typeId)) + getTimeTip(itemsTime_list[temp_vo.targetId]);
            ToolTipManager.getInstance().setToolTip(obj, tipStr);
            Debug.Log("tipStr: " + tipStr);
        }
        else if (temp_vo is PresentVO)
        {
            ToolTipManager.getInstance().setToolTip(obj, StringUtils.handleTip(getPresentTipInfo(temp_vo.typeId)));
        }
    }

    private static string getGoodsToolTip(int _id)
    {
        List<string> str = null;
        string returnStr = "";
        if (GoodsProxy.goodInfo_map != null && GoodsProxy.goodInfo_map.Count > 0)
        {
            str = new List<string> { GoodsProxy.goodInfo_map[_id].name, GoodsProxy.goodInfo_map[_id].intro };
            if (str != null && str.Count == 2)
            {
                returnStr = str[0] + "\n" + str[1];
            }
        }
        return returnStr;
    }

    public static string getTimeTip(int time)
    {
        string returnStr = "";
        if (time != -1)
        {
            returnStr += "\n<color=#13ec21>道具时效：" + getUpTime(time) + "</color>";
        }
        return returnStr;
    }

    private static string getUpTime(int time)
    {
        int date = (int)Math.Ceiling((double)(Convert.ToInt32(TimeUtils.getFormatTime(time).Split(":")[0]) / 24));
        return date + "天";
    }

    private static string getPresentTipInfo(int _id)
    {
        List<string> str = null;
        string returnStr = "";
        if (GoodsProxy.presentMap != null && GoodsProxy.presentMap.Count > 0)
        {
            str = GoodsProxy.presentMap[_id];
            if (str != null && str.Count == 2)
            {
                returnStr = str[0] + "\n" + str[1];
            }
        }
        return returnStr;
    }

    public static void clearGoodsSceneMap(string sceneName)
    {
        if (goodsSceneMap.ContainsKey(sceneName))
        {
            goodsSceneMap.Remove(sceneName);
        }
    }

    private static string getToolTipContent(EquipmentVO vo)
    {
        return ToolTipUtils.equipmentTip(vo, new List<EquipmentVO> { });
    }

}