{"format": 1, "restore": {"D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Runtime.csproj": {}}, "projects": {"D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Runtime.csproj", "projectName": "UnityWebSocket.Runtime", "projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UnityWebSocket.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\Temp\\obj\\UnityWebSocket.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.202\\RuntimeIdentifierGraph.json"}}}}}