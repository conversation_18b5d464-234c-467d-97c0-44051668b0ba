syntax = "proto3";

package pb;

option go_package = "/pb";
  
message PlayerGoodsResponse {
    int32 result = 1;
    repeated PropSimple props = 2; // 物品列表
}

message PropSimple {
    int32 num = 1;
    int32 targetId = 2;
    sint32 time = 3;
    int32 typeId = 4;
    int32 part = 5;
    int32 sn = 6;
}

message PlayerEquipmentRequest {
    int32 sort = 1;
}


message PlayerEquipmentResponse {
    int32 result = 1;
    int32 sort = 2;
    repeated EquipmentSimple equipment = 3; // 装备列表
}


message EquipmentSimple {
    int32 targetId = 1;
    int32 typeId = 2;
    int32 num = 3;
}