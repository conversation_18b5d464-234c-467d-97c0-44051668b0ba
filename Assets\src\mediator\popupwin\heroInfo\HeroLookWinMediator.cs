using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class HeroLookWinMediator
{
  public const string NAME = "HeroLookWinMediator";

  private int hero_id = 0;

  private int hero_race = 0;

  private HeroVO hero_vo;

  private int castle_id = 0;

  private CastleVO castle_vo;

  private ObjectPropertyProxy objectPropertyProxy;

  private string now_edite_type = "";

  private EquipmentProxy equipmentProxy;

  private ScreenProxy screenProxy;

  private PopupWinFrame win;

  public HeroLookWinMediator(PopupWinFrame viewObject)
  {
    win = viewObject;
    listNotificationInterests();
    init();
    initListner();
  }

  private void init()
  {
    toggleBar.addButton(LanguageManager.getPopupWin("LABEL_HEROLOOK_ROLE"));
    toggleBar.addButton(LanguageManager.getPopupWin("LABEL_HEROLOOK_EQUIP"));
    toggleBar.addButton(LanguageManager.getPopupWin("LABEL_HEROLOOK_SKILL"));
    actionBtn.label = LanguageManager.getScene("LABEL_HE_UNLOAD");
    actionSP.visible = false;
    toggleBar.selectedIndex = 0;
    changeHandler();
    objectPropertyProxy = ProxyManager.Instance.objectPropertyProxy;
    hero_id = (int)win.data;
    equipmentProxy = ProxyManager.Instance.equipmentProxy;
    castle_id = CurrentCastleIdUtils.castleId;
    objectPropertyProxy.getObjectPropertyCallBack(castle_id, getCastleVO);
    MessageBus.Publish(PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY, hero_id);
    getHeroVO();
  }

  private void initListner()
  {
    win.OnClose.AddListener(() =>
    {
      onCloseHandler();
    });
    toggleBar.OnChange.AddListener(() =>
    {
      changeHandler();
    });
    heroChooser.OnChange.AddListener(() =>
    {
      chooserChangeHandler();
    });
    closeButton.OnClick.AddListener((btn) =>
    {
      closeHandler();
    });
    actionBtn.OnClick.AddListener((btn) =>
    {
      upDateHandler();
    });
    EventTrigger eventTrigger = actionSP.gameObject.GetComponent<EventTrigger>();
    if (eventTrigger == null)
    {
      eventTrigger = actionSP.gameObject.AddComponent<EventTrigger>();
    }
    actionSP.AddEventTrigger(eventTrigger, EventTriggerType.PointerDown, hideBtnHandler);
  }

  private void removeListener()
  {
    win.OnClose.RemoveListener(() =>
    {
      onCloseHandler();
    });
    toggleBar.OnChange.RemoveListener(() =>
    {
      changeHandler();
    });
    heroChooser.OnChange.RemoveListener(() =>
    {
      chooserChangeHandler();
    });
    closeButton.OnClick.RemoveListener((btn) =>
    {
      closeHandler();
    });
    actionBtn.OnClick.RemoveListener((btn) =>
    {
      upDateHandler();
    });

  }

  private void onCloseHandler()
  {
    MediatorManager.getInstance().RemoveMediator(NAME);
  }

  private void closeHandler()
  {
    win.close();
  }

  private void hideBtnHandler(BaseEventData arg0)
  {
    actionSP.visible = false;
  }

  private void upDateHandler()
  {
    switch (now_edite_type)
    {
      case HeroSkillView.CLICK_TYPE:
        refeashSkill();
        break;
      case HeroEquipmentView.CLICK_TYPE:
        refeashEquipment();
        break;
    }
  }

  private void refeashSkill()
  {
    // var skill:MessageByteArray = null;
    // int i = 0;
    // if (heroSkillView.now_type == 0)
    // {
    //   if (heroSkillView.updateAry != null)
    //   {
    //     skill = new MessageByteArray();
    //     skill.writeCustomMsg(6);
    //     skill.writeCustomInt(SocketProxy.CMTS_HERO_ADD_SKILL);
    //     skill.writeCustomInt(CurrentCastleIdUtils.castleId);
    //     skill.writeCustomInt(hero_id);
    //     skill.writeCustomInt(0);
    //     skill.writeCustomIntheroSkillView.unloadIndex);
    //     skill.writeCustomInt(0);
    //     SocketManager.getInstance().send(skill);
    //     heroSkillView.updateAry = null;
    //   }
    // }
    // else if (heroSkillView.now_type == 1)
    // {
    //   facade.sendNotification(ApplicationFacade.EVENT_OPEN_WIN, null, ModuleUtils.POPUPWIN_HEROUNREELWIN);
    // }
  }

  private void refeashEquipment()
  {

  }

  private void changeHandler()
  {
    // removeAllMediator();
    switch (toggleBar.selectedIndex)
    {
      case 0:
        charactorBorderPanel.visible = true;
        equipmentBorderPanel.visible = false;
        skillBorderPanel.visible = false;
        MediatorManager.getInstance().RegisterMediator(HeroPointMediator.NAME, win);
        sendNote();
        break;
      case 1:
        equipmentBorderPanel.visible = true;
        charactorBorderPanel.visible = false;
        skillBorderPanel.visible = false;
        MediatorManager.getInstance().RegisterMediator(HeroEquipmentMediator.NAME, win);
        sendNote();
        break;
      case 2:
        skillBorderPanel.visible = true;
        charactorBorderPanel.visible = false;
        equipmentBorderPanel.visible = false;
        MediatorManager.getInstance().RegisterMediator(HeroSkillMediator.NAME, win);
        sendNote();
        break;
    }
  }

  private void removeAllMediator()
  {
    MediatorManager.getInstance().RemoveMediator(HeroPointMediator.NAME);
    MediatorManager.getInstance().RemoveMediator(HeroEquipmentMediator.NAME);
    MediatorManager.getInstance().RemoveMediator(HeroSkillMediator.NAME);
  }

  private void chooserChangeHandler()
  {
    // var tf:TextFormat = new TextFormat();
    // tf.color = 16777215;
    // tf.font = LanguageManager.getScene("GAME_FONT");
    // tf.size = 12;
    // heroChooser.textField.setStyle("textFormat", tf);
    hero_id = (int)heroChooser.selectedItem.data;
    Debug.Log("Change hero_id:" + hero_id);
    sendNote();
    getHeroVO();
  }

  private void sendNote()
  {
    if (_hero_id > 0)
    {
      MessageBus.Publish(PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY, _hero_id);
    }
  }
  private void insertHeroViewData(List<GameObjectVO> p_data)
  {
    HeroVO temp_vo = null;
    if (p_data != null && p_data.Count > 0)
    {
      temp_vo = p_data[0] as HeroVO;
      if (temp_vo.targetId != _hero_id)
      {
        return;
      }
      // hero_vo = new HeroVO();
      hero_vo = temp_vo;
      actionSP.visible = false;
      heroEquipmentView.setHeroVO(hero_vo);
      heroSkillView.setHeroVO(hero_vo);
    }
  }


  private void getHeroVO()
  {
    objectPropertyProxy.getObjectPropertyCallBack(hero_id, insertHeroViewData);
  }

  private void getCastleVO(List<GameObjectVO> p_data)
  {
    if (p_data == null || p_data.Count == 0)
    {
      return;
    }
    CastleVO temp_vo = null;
    if (p_data[0] is CastleVO)
    {
      temp_vo = p_data[0] as CastleVO;
      if (temp_vo.targetId == castle_id)
      {
        if (castle_vo != null)
        {
          castle_vo = temp_vo;
          insertChooserData();
        }
        else
        {
          castle_vo = temp_vo;
          insertChooserData();
        }
      }
    }
  }

  private void insertChooserData()
  {
    List<int> list = castle_vo.heros;
    if (list != null && list.Count > 0)
    {
      objectPropertyProxy.getQueueByIdList(list, getHeroListVO, ObjectPropertyProxy.GAMEOBJECT_TYPE_OBJECT);
    }
  }
  private void getHeroListVO(List<GameObjectVO> p_data)
  {
    List<HeroVO> volist = null;
    HeroVO herovo = null;
    int index = 0;
    int i = 0;
    // var tf:TextFormat = null;
    if (p_data != null && p_data.Count > 0)
    {
      volist = p_data.ConvertAll(item => item as HeroVO).FindAll(item => item != null);
      heroChooser.OnChange.RemoveListener(() =>
    {
      chooserChangeHandler();
    });
      heroChooser.removeAll();
      for (i = 0; i < volist.Count; i++)
      {
        herovo = volist[i];
        if (herovo.targetId == hero_id)
        {
          index = i;
        }
        heroChooser.AddItem(herovo.name, herovo.targetId);
      }
      // heroChooser.selectedIndex = index;
      heroChooser.SetDefaultItem(volist[index].targetId);
      // tf = new TextFormat();
      // tf.color = 16777215;
      // tf.font = LanguageManager.getScene("GAME_FONT");
      // tf.size = 12;
      // heroChooser.setStyle("textFormat", tf);
      heroChooser.OnChange.AddListener(() =>
      {
        chooserChangeHandler();
      });
    }
  }

  public void getUnReelNum()
  {
    UnReelInfo unReelInfo = new()
    {
      id = heroSkillView.action_id,
      hero = hero_id
    };
    MessageBus.Publish(PopupWinEvent.FOR_UNREEL_NUM, unReelInfo);
  }

  private void xmlReady()
  {
    heroSkillView.setkeelMap();
  }

  public void listNotificationInterests()
  {
    MessageBus.SubscribeMany(new string[] { PopupWinEvent.HEROLOOKWINMEDIATOR_CREATE_COMPLETE, ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO, ObjectPropertyProxy.MSG_OBJECT_UPDATE_CASTLE, PopupWinEvent.GET_UNREEL_NUM, EquipmentProxy.GET_EQUIPMENT_TOOLTIP_LIST }, handleNotification);
  }

  private void removeListNotificationInterests()
  {
    MessageBus.UnsubscribeMany(new string[] { PopupWinEvent.HEROLOOKWINMEDIATOR_CREATE_COMPLETE, ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO, ObjectPropertyProxy.MSG_OBJECT_UPDATE_CASTLE, PopupWinEvent.GET_UNREEL_NUM, EquipmentProxy.GET_EQUIPMENT_TOOLTIP_LIST }, handleNotification);
  }

  public void handleNotification(string messageType, object data, string eventType)
  {
    switch (messageType)
    {
      case PopupWinEvent.HEROLOOKWINMEDIATOR_CREATE_COMPLETE:
        break;
      case ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO:
        insertHeroViewData(data as List<GameObjectVO>);
        break;
      case ObjectPropertyProxy.MSG_OBJECT_UPDATE_CASTLE:
        getCastleVO(data as List<GameObjectVO>);
        break;
      case PopupWinEvent.GET_UNREEL_NUM:
        getUnReelNum();
        break;
      case EquipmentProxy.GET_EQUIPMENT_TOOLTIP_LIST:
        xmlReady();
        break;
    }
  }

  public void onRemove()
  {
    removeListNotificationInterests();
    removeListener();
    delObj();
  }



  private void delObj()
  {
    removeAllMediator();
    actionBtn.gameObject.SetActive(false);
    actionSP.gameObject.SetActive(false);
    heroEquipmentView.onRemove();
    heroSkillView.onRemove();
  }

  public int _hero_id
  {
    get => hero_id;
  }


  private CommonToggleButtonBar toggleBar
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/toggleBar").GetComponent<CommonToggleButtonBar>();
    }
  }

  private CommonComboBox heroChooser
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/heroChooser").GetComponent<CommonComboBox>();
    }
  }

  private CommonImageLoader hero_kuang
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/hero_kuang").GetComponent<CommonImageLoader>();
    }
  }

  private HeroEquipmentView heroEquipmentView
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/herophotogoldBorderPanel/HeroEquipmentView").GetComponent<HeroEquipmentView>();
    }
  }

  private HeroSkillView heroSkillView
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/herophotogoldBorderPanel/heroSkillView").GetComponent<HeroSkillView>();
    }
  }

  private RightCornerBorderPanel charactorBorderPanel
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/charactorBorderPanel").GetComponent<RightCornerBorderPanel>();
    }
  }
  private RightCornerBorderPanel equipmentBorderPanel
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/equipmentBorderPanel").GetComponent<RightCornerBorderPanel>();
    }
  }
  private RightCornerBorderPanel skillBorderPanel
  {
    get
    {
      return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/skillBorderPanel").GetComponent<RightCornerBorderPanel>();
    }
  }
  private ScaleSprite actionSP
  {
    get
    {
      return win.FindObjectByPath("win/actionLayer/actionSP").GetComponent<ScaleSprite>();
    }
  }

  private SmallButton actionBtn
  {
    get
    {
      return win.FindObjectByPath("win/actionLayer/actionSP/actionBtn").GetComponent<SmallButton>();
    }
  }

  private ConfirmButton closeButton
  {
    get
    {
      return win.FindObjectByPath("win/buttonPanel/confirmButton1").GetComponent<ConfirmButton>();
    }
  }
}


public class UnReelInfo
{
  public int id;
  public int hero;
}