﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_3;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AUDIO;ENABLE_CLOTH;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_WEBGL;UNITY_WEBGL;UNITY_WEBGL_API;UNITY_DISABLE_WEB_VERIFICATION;UNITY_GFX_USE_PLATFORM_VSYNC;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;UNITY_POST_PROCESSING_STACK_V2;LETAI_TRUESHADOW;VUPLEX_CCU;DOTWEEN;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>WebGL:20</UnityBuildTarget>
    <UnityVersion>6000.1.3f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="D:\software\unity3d\6000.1.3f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\software\unity3d\6000.1.3f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="D:\software\unity3d\6000.1.3f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\src\socket\pb\EquipmentVO.cs" />
    <Compile Include="Assets\src\vo\gameObject\WorldResourceVO.cs" />
    <Compile Include="Assets\src\utils\popupwin\ISoldierBox.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\ListView\LoopListViewItem2.cs" />
    <Compile Include="Assets\src\controls\checkBox\CommonCheckBoxBasic.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventHandler.cs" />
    <Compile Include="Assets\src\vo\gameObject\WorldItemVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SimpleItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\DraggableView\DraggableViewFadeTopToBottomDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelSpecial.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\SimpleExpandItemData.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewPullDownRefreshDemoScript.cs" />
    <Compile Include="Assets\src\socket\pb\GoodsProxy.cs" />
    <Compile Include="Assets\src\vo\barracks\TrainingSodierVO.cs" />
    <Compile Include="Assets\src\controls\container\UILoader.cs" />
    <Compile Include="Assets\src\manager\ResPathManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\TextDescRowColItemList.cs" />
    <Compile Include="Assets\src\controls\TextArea.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\EnvMapAnimator.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\Common\ItemPosMgr.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\EquipmentBorder.cs" />
    <Compile Include="Assets\src\socket\pb\CastleVO.cs" />
    <Compile Include="Assets\src\controls\button\CommonToggleButtonBasic.cs" />
    <Compile Include="Assets\src\utils\ArmyPowerUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\GridView\GridItemGroup.cs" />
    <Compile Include="Assets\src\controls\display\ScaleSprite.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_A.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\DeleteAnimationItem.cs" />
    <Compile Include="Assets\src\view\main\bottom\BottomPanelMediator.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\TreeViewItemData.cs" />
    <Compile Include="Assets\src\manager\ProxyManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewSimpleDemo.cs" />
    <Compile Include="Assets\src\view\component\CastleListButton.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\SimpleItemData.cs" />
    <Compile Include="Assets\src\controls\menu\GamePlayerMenu.cs" />
    <Compile Include="Assets\src\vo\gameObject\PresentVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelNestedSimple.cs" />
    <Compile Include="Assets\src\controls\image\TransparentRaycastImage.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\GridView\LoopGridItemPool.cs" />
    <Compile Include="Assets\src\vo\gameObject\SkillVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewMultiplePrefabDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\DragEventHelper.cs" />
    <Compile Include="Assets\src\view\main\bottom\heroPage\HeroPanel.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SpinPickerItem.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\StaggeredGridView\LoopStaggeredGridView.cs" />
    <Compile Include="Assets\src\controls\border\RightCornerBorderPanel.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\IconTextDescItem.cs" />
    <Compile Include="Assets\src\controls\button\SimpleButton.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\TreeView\TreeViewSimpleDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\IconTextItem.cs" />
    <Compile Include="Assets\src\manager\DataBufferManager.cs" />
    <Compile Include="Assets\src\manager\LoginManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\DraggableHorizonalItem.cs" />
    <Compile Include="Assets\src\view\main\top\TopPanelMediator.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewDiagonalDemoScript.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroPointMediator.cs" />
    <Compile Include="Assets\src\events\DataChangeEvent.cs" />
    <Compile Include="Assets\src\controls\uiLoader\CommonUILoader.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewClickLoadMoreDemoScript.cs" />
    <Compile Include="Assets\src\Addressable\SceneLoader.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark02.cs" />
    <Compile Include="Assets\src\utils\popupwin\GoldSoldierPanel.cs" />
    <Compile Include="Assets\src\vo\gameObject\BuildVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\StaggeredView\StaggeredViewSimpleLeftToRightDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseHorizontalItemList.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedGridViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelLoad.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SliderItemList.cs" />
    <Compile Include="Assets\src\proxy\HeroOutInfoProxy.cs" />
    <Compile Include="Assets\src\proxy\GoodsProxy.cs" />
    <Compile Include="Assets\src\utils\ServiceUtils.cs" />
    <Compile Include="Assets\src\controls\container\RightCornerBorderUILoader.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ChatViewItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\PageViewItem.cs" />
    <Compile Include="Assets\src\events\ComponentEvent.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\NestedItemData.cs" />
    <Compile Include="Assets\src\socket\pb\EquipmentProxy.cs" />
    <Compile Include="Assets\src\vo\gameObject\FieldBuildVO.cs" />
    <Compile Include="Assets\src\data\PopupwinData.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewClickLoadMoreDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelDelete.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ToggleItem.cs" />
    <Compile Include="Assets\src\vo\OpenWinVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\IconItemList.cs" />
    <Compile Include="Assets\UnityChan\SplashScreen\Scripts\SplashScreen.cs" />
    <Compile Include="Assets\src\Addressable\AddressableManager.cs" />
    <Compile Include="Assets\src\vo\login\MapInfoVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelSpecialLoad.cs" />
    <Compile Include="Assets\src\events\DataChangeType.cs" />
    <Compile Include="Assets\src\view\main\top\buildname\BuildNameLabel.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewSimpleFilterDemoScript.cs" />
    <Compile Include="Assets\src\mediator\popupwin\feature\FeatureTools.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\StaggeredView\StaggeredViewSimpleTopToBottomDemoScript.cs" />
    <Compile Include="Assets\src\vo\gameObject\OreMineVO.cs" />
    <Compile Include="Assets\src\events\PopWinEvent.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\GridView\LoopGridView.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewPullDownRefreshDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelTreeViewSimple.cs" />
    <Compile Include="Assets\src\view\main\PlayerPage\PlayerPanel.cs" />
    <Compile Include="Assets\src\utils\GameObjectUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\InputFieldItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelGridViewDelete.cs" />
    <Compile Include="Assets\src\core\UIComponent.cs" />
    <Compile Include="Assets\src\events\UnityEventManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\LoadClickItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\DragEventForward.cs" />
    <Compile Include="Assets\src\view\main\bottom\MainLeftTopMsg.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\StaggeredGridView\LoopStaggeredGridViewItem.cs" />
    <Compile Include="Assets\src\utils\DebugUtils.cs" />
    <Compile Include="Assets\src\controls\itemRenderer\RightCornerCountUILoader.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SimpleItemList.cs" />
    <Compile Include="Assets\src\manager\PrefabManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\ExpandAnimationType.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroSkillView.cs" />
    <Compile Include="Assets\src\manager\ToolTipManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\TreeView\TreeViewAddAndRemoveDemoScript.cs" />
    <Compile Include="Assets\src\controls\scrollPane\ItemUI.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewMultiplePrefabLeftToRightDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewMultiplePrefabDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SliderItem.cs" />
    <Compile Include="Assets\src\vo\gameObject\BossVO.cs" />
    <Compile Include="Assets\src\manager\LanguageManager.cs" />
    <Compile Include="Assets\src\controls\anime\DOTweenMovieClip.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewSimpleLoadMoreDemo.cs" />
    <Compile Include="Assets\src\utils\SecurityUtils.cs" />
    <Compile Include="Assets\src\view\main\ApplicationFacade.cs" />
    <Compile Include="Assets\src\vo\main\NetSpeedStateVO.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_ExampleScript_01.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMPro_InstructionOverlay.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewPullUpLoadMoreDemo.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\GalleryVerticalItem.cs" />
    <Compile Include="Assets\src\view\main\bottom\component\InnerCenterButtonInfo.cs" />
    <Compile Include="Assets\src\utils\StringUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedSimpleGridViewDemoScript.cs" />
    <Compile Include="Assets\src\vo\gameObject\NaturalBuildVO.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeB.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewBottomToTopDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\TreeViewItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\TreeViewDataSourceMgr.cs" />
    <Compile Include="Assets\src\utils\popupwin\CurrentCastleIdUtils.cs" />
    <Compile Include="Assets\src\data\HeroStaticData.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroLookWinMediator.cs" />
    <Compile Include="Assets\src\view\main\bottom\heroPage\HeroItemRenderer.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseHorizontalToggleItemList.cs" />
    <Compile Include="Assets\src\view\main\top\TopPanel.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewExpandDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ExpandItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewFeatureDemoScript.cs" />
    <Compile Include="Assets\src\manager\MediatorManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\DraggableView\DraggableViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets\src\vo\gameObject\PrestigeVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\FPSDisplay.cs" />
    <Compile Include="Assets\src\utils\list\IListPage.cs" />
    <Compile Include="Assets\src\controls\scrollPane\OptimizedScrollView.cs" />
    <Compile Include="Assets\src\view\main\bottom\component\BottomCastleNameText.cs" />
    <Compile Include="Assets\src\vo\gameObject\WorldNPCVO.cs" />
    <Compile Include="Assets\src\controls\button\CommonToggleButton.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SimpleLoadItem.cs" />
    <Compile Include="Assets\src\vo\gameObject\KingCastleVO.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexJitter.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedListViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\IconTextItemList.cs" />
    <Compile Include="Assets\UnityWebSocket\Demo\UnityWebSocketDemo.cs" />
    <Compile Include="Assets\src\view\main\bottom\BottomPanel.cs" />
    <Compile Include="Assets\src\utils\FilterEffectUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\GalleryHorizontalItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelTreeView.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\Chat\ChatViewDemoScript.cs" />
    <Compile Include="Assets\src\socket\pb\ServerMediator.cs" />
    <Compile Include="Assets\src\proxy\ChatProxy.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\TreeView\TreeViewWithStickyHeadDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewSimpleDemoScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_UiFrameRateCounter.cs" />
    <Compile Include="Assets\src\controls\button\ToggleButtonUnselected.cs" />
    <Compile Include="Assets\src\utils\CConfig.cs" />
    <Compile Include="Assets\src\controls\container\CommonViewStack.cs" />
    <Compile Include="Assets\src\controls\numericStepper\CommonNumericStepperBasic.cs" />
    <Compile Include="Assets\src\proxy\BuildManageProxy.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\ListView\LoopListView2.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\NestedGridViewLeftRightItem.cs" />
    <Compile Include="Assets\src\controls\button\ConfirmButton.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexZoom.cs" />
    <Compile Include="Assets\src\events\EventManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewDiagonalSelectDeleteDemoScript.cs" />
    <Compile Include="Assets\src\socket\pb\BuildListVO.cs" />
    <Compile Include="Assets\src\controls\numericStepper\CommonNumericStepper.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListViewAnimation\ListViewDeleteAnimationDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ImageItemList.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_B.cs" />
    <Compile Include="Assets\src\view\login\component\login_input_field.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseRowColItemList.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\ItemData.cs" />
    <Compile Include="Assets\src\controls\button\LabelButton.cs" />
    <Compile Include="Assets\src\view\popupwin\basic\AlertDesktop.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseHorizontalToggleItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpinView\SpinDateTimePickerDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewFilterDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\IconTextDescItemList.cs" />
    <Compile Include="Assets\src\model\MainModelLocator.cs" />
    <Compile Include="Assets\src\mediator\popupwin\CastleFightWinCom.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SkewTextExample.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelGridView.cs" />
    <Compile Include="Assets\src\proxy\ObjectPropertyProxy.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\WarpTextExample.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ToggleItemList.cs" />
    <Compile Include="Assets\src\mediator\popupwin\kingdom\SeegoodsWinMediator.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpinView\SpinTimePickerDemoScript.cs" />
    <Compile Include="Assets\src\vo\guild\GuildVO.cs" />
    <Compile Include="Assets\src\socket\pb\GameObjectVO.cs" />
    <Compile Include="Assets\src\view\main\top\component\CastleBuildSpriteRenderButton.cs" />
    <Compile Include="Assets\src\controls\container\GoldBorderUILoader.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\Gallery\GalleryVerticalDemoScript.cs" />
    <Compile Include="Assets\src\view\main\top\buildname\BuildNameLabelManager.cs" />
    <Compile Include="Assets\src\utils\ChatUtils.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ShaderPropAnimator.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\AddAnimationItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelNested.cs" />
    <Compile Include="Assets\src\vo\gameObject\EquipmentVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\ChatMsgDataSourceMgr.cs" />
    <Compile Include="Assets\src\core\CoroutineHelper.cs" />
    <Compile Include="Assets\src\controls\textInput\CommonTextInput.cs" />
    <Compile Include="Assets\src\controls\button\ToggleButtonBasic.cs" />
    <Compile Include="Assets\src\controls\button\IToggleButtonBasic.cs" />
    <Compile Include="Assets\src\vo\gameObject\CastleVO.cs" />
    <Compile Include="Assets\src\utils\GoodsInfoBindAsync.cs" />
    <Compile Include="Assets\src\vo\gameObject\SoldierVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\StaggeredView\StaggeredViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedSimpleListViewDemoScript.cs" />
    <Compile Include="Assets\src\proxy\EquipmentProxy.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\AnimationHelper.cs" />
    <Compile Include="Assets\src\controls\comboBox\ComboBox.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SliderComplexItemList.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewPullDownRefreshOrPullUpLoadDemo.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewSelectDeleteDemoScript.cs" />
    <Compile Include="Assets\src\utils\StageUtils.cs" />
    <Compile Include="Assets\src\vo\login\LoginData.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextInfoDebugTool.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\RotateScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedGridViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\IconItem.cs" />
    <Compile Include="Assets\src\controls\scrollPane\SimpleScrollView.cs" />
    <Compile Include="Assets\src\socket\pb\ChatProxy.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\NestedLeftRightItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\Chat\ChatViewChangeViewportHeightScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeA.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\DragEventHelperEx.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\DraggableItemData.cs" />
    <Compile Include="Assets\src\vo\gameObject\HeroVO.cs" />
    <Compile Include="Assets\src\controls\border\RoundCornerBorderPanel.cs" />
    <Compile Include="Assets\src\vo\gameObject\PlayerVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\StaggeredView\StaggeredViewMoveToItemDemoScript.cs" />
    <Compile Include="Assets\src\view\main\top\CastleScenePrefab.cs" />
    <Compile Include="Assets\src\core\InvalidationType.cs" />
    <Compile Include="Assets\src\controls\button\MenuButton.cs" />
    <Compile Include="Assets\src\proxy\MainProxy.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseVerticalItem.cs" />
    <Compile Include="Assets\src\controls\uiLoader\CommonUILoaderBasic.cs" />
    <Compile Include="Assets\src\controls\checkBox\CheckBox.cs" />
    <Compile Include="Assets\src\utils\MaxUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ResponsiveView\ResponsiveViewDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ExpandAnimationItem.cs" />
    <Compile Include="Assets\src\controls\NumericStepper.cs" />
    <Compile Include="Assets\src\controls\menu\GameMenuItem.cs" />
    <Compile Include="Assets\src\utils\ModuleUtils.cs" />
    <Compile Include="Assets\src\vo\gameObject\BattleBuildVO.cs" />
    <Compile Include="Assets\src\controls\list\CommonListBasic.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01_UGUI.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SimpleScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelGallery.cs" />
    <Compile Include="Assets\src\view\popupwin\basic\IPopUpWin.cs" />
    <Compile Include="Assets\src\view\main\top\buildname\IBuildNameLabel.cs" />
    <Compile Include="Assets\src\events\TopPanelEvent.cs" />
    <Compile Include="Assets\src\controls\button\CommonToggleButtonBar.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark04.cs" />
    <Compile Include="Assets\src\manager\AnimeFrameManager.cs" />
    <Compile Include="Assets\src\controls\border\GoldBorderPanel.cs" />
    <Compile Include="Assets\src\view\main\component\CommonSpriteButtonController.cs" />
    <Compile Include="Assets\src\mediator\Mediator.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ChatController.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\Gallery\GalleryHorizontalDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\PageView\PageViewSimpleDemoScript.cs" />
    <Compile Include="Assets\src\view\main\component\CommonImageButtonController.cs" />
    <Compile Include="Assets\src\model\LoginModelLocator.cs" />
    <Compile Include="Assets\src\vo\guild\ProvinceVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ContentFitterItem.cs" />
    <Compile Include="Assets\src\utils\NumberUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\Common\ClickEventListener.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpinView\SpinDatePickerDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedListViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_DigitValidator.cs" />
    <Compile Include="Assets\src\proxy\FeatureProxy.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_FrameRateCounter.cs" />
    <Compile Include="Assets\src\proxy\SkillProxy.cs" />
    <Compile Include="Assets\src\vo\gameObject\ExclusiveBoss.cs" />
    <Compile Include="Assets\src\manager\Alert.cs" />
    <Compile Include="Assets\src\proxy\SocketProxy.cs" />
    <Compile Include="Assets\src\events\IEventManager.cs" />
    <Compile Include="Assets\src\socket\pb\BuildVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\AnimationType.cs" />
    <Compile Include="Assets\src\controls\label\CommonTextMeshPro.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\StaggeredGridView\StaggeredGridItemGroup.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ToggleRowColItem.cs" />
    <Compile Include="Assets\src\vo\Alcazarlist\CastleListVO.cs" />
    <Compile Include="Assets\src\controls\comboBox\CommonComboBox.cs" />
    <Compile Include="Assets\src\view\popupwin\basic\PopupWinFrame.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshSpawner.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\OneDirectionDragHelper.cs" />
    <Compile Include="Assets\src\proxy\StakeProxy.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroEquipmentView.cs" />
    <Compile Include="Assets\src\view\main\GeneralMoveScript.cs" />
    <Compile Include="Assets\src\events\EventTriggerButtonController.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewPullUpLoadMoreDemoScript.cs" />
    <Compile Include="Assets\src\utils\popupwin\PlayerIdUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelTreeViewSticky.cs" />
    <Compile Include="Assets\src\vo\gameObject\OwnedNaturalVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListViewAnimation\ListViewAddAnimationDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewSelectDeleteDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Res\ResManager.cs" />
    <Compile Include="Assets\src\controls\button\SmallButton.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseHorizontalItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\TreeView\TreeViewWithChildIndentDemo.cs" />
    <Compile Include="Assets\src\utils\popupwin\SolidierNumPanelManager.cs" />
    <Compile Include="Assets\src\shader\UltimateEdgeFix.cs" />
    <Compile Include="Assets\src\socket\pb\PlayerVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\DescList.cs" />
    <Compile Include="Assets\src\socket\pb\HeroVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\NestedView\NestedSimpleSpecialGridViewDemoScript.cs" />
    <Compile Include="Assets\src\controls\TextInput.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\InputFieldItemList.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\CameraController.cs" />
    <Compile Include="Assets\src\view\component\equ\EquBtn.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\PageView\PageViewDemoScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark03.cs" />
    <Compile Include="Assets\src\socket\WebSocketManager.cs" />
    <Compile Include="Assets\src\manager\InputSystemManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelSpecialDelete.cs" />
    <Compile Include="Assets\src\controls\container\CommonImageLoader.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\LoadItem.cs" />
    <Compile Include="Assets\src\socket\pb\Message.cs" />
    <Compile Include="Assets\src\utils\HerosProTipsUtils.cs" />
    <Compile Include="Assets\src\controls\button\CommonButton.cs" />
    <Compile Include="Assets\src\view\main\top\component\CastleBuildImageButton.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\NestedTopBottomItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewSimpleTopToBottomDemoScript.cs" />
    <Compile Include="Assets\src\controls\border\GoldAreaBorderPanel.cs" />
    <Compile Include="Assets\src\vo\gameObject\TransmitDoorVO.cs" />
    <Compile Include="Assets\src\vo\build\BuildListVO.cs" />
    <Compile Include="Assets\src\data\DataProvider.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseRowColItem.cs" />
    <Compile Include="Assets\src\mediator\main\MainLoaderMediator.cs" />
    <Compile Include="Assets\src\controls\anime\SimpleMovieClip.cs" />
    <Compile Include="Assets\src\view\component\ImageListButtonBar.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\Common\CommonDefine.cs" />
    <Compile Include="Assets\src\controls\button\BorderDropShadowButton.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\DraggableView\DraggableViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\TweenHelper.cs" />
    <Compile Include="Assets\src\events\MainButtonEvent.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelStaggeredView.cs" />
    <Compile Include="Assets\Scripts\BasicGridAdapter.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanel.cs" />
    <Compile Include="Assets\src\vo\gameObject\ArmyPortVO.cs" />
    <Compile Include="Assets\src\socket\pb\SkillProxy.cs" />
    <Compile Include="Assets\src\mediator\main\MainButtonRegister.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\NestedSimpleLeftRightItem.cs" />
    <Compile Include="Assets\src\mediator\popupwin\kingdom\kingdomCom\LockIcon.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\NestedSimpleItemData.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewContentFitterDemoScript.cs" />
    <Compile Include="Assets\src\controls\list\SimpleList.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\SpecialGridView\SpecialGridViewSimpleLeftToRightDemoScript.cs" />
    <Compile Include="Assets\src\view\popupwin\basic\AlertWindow.cs" />
    <Compile Include="Assets\src\controls\textInput\FixedCaretInputField.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\TreeViewItemCountMgr.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\AutoSetAnchorPosForIphonex.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\DraggableVerticalItem.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\DropdownSample.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexColorCycler.cs" />
    <Compile Include="Assets\src\manager\PopUpWinManager.cs" />
    <Compile Include="Assets\src\utils\ListUtils.cs" />
    <Compile Include="Assets\src\mediator\popupwin\kingdom\SeegoodCell.cs" />
    <Compile Include="Assets\src\events\MessageBus.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewSelectDeleteDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Base\DragChangSizeScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\TreeViewItemHead.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\StaggeredView\StaggeredViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets\src\utils\SetHerolevelUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\ListView\LoopListItemPool.cs" />
    <Compile Include="Assets\src\view\main\bottom\CastleGouZi.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\ItemDataBase.cs" />
    <Compile Include="Assets\src\vo\gameObject\BabelVO.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\GridView\LoopGridViewItem.cs" />
    <Compile Include="Assets\src\controls\checkBox\CommonCheckBox.cs" />
    <Compile Include="Assets\src\utils\SkillNameUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewRightToLeftDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelGridViewLoad.cs" />
    <Compile Include="Assets\src\vo\battle\BattleCommandVO.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewSimpleLoopDemoScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ObjectSpin.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\DataSourceMgr.cs" />
    <Compile Include="Assets\src\socket\pb\ObjectPropertyProxy.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\NestedSimpleGridViewTopBottomItem.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_PhoneNumberValidator.cs" />
    <Compile Include="Assets\src\utils\SoldierRaceUtils.cs" />
    <Compile Include="Assets\src\controls\container\RoundCornerBorderUILoader.cs" />
    <Compile Include="Assets\src\vo\gameObject\GameObjectVO.cs" />
    <Compile Include="Assets\src\controls\ButtonLabelPlacement.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\TreeViewSimpleItem.cs" />
    <Compile Include="Assets\src\socket\pb\Login.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\DataSource\ContentFitterItemData.cs" />
    <Compile Include="Assets\src\controls\tooltip\ToolTipTrigger.cs" />
    <Compile Include="Assets\src\test\TestLogin.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewDemoScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextConsoleSimulator.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewSimpleDiagonalDemoScript.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroSkillMediator.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TeleType.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelMenu.cs" />
    <Compile Include="Assets\src\utils\XMLUtils.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListViewAnimation\ListViewExpandAnimationDemoScript.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventCheck.cs" />
    <Compile Include="Assets\src\utils\list\ListPage.cs" />
    <Compile Include="Assets\src\utils\KeyUtils.cs" />
    <Compile Include="Assets\src\controls\textArea\CommonTextArea.cs" />
    <Compile Include="Assets\src\controls\button\ToggleButtonBarBasic.cs" />
    <Compile Include="Assets\src\controls\textArea\CommonTextAreaBasic.cs" />
    <Compile Include="Assets\src\Addressable\AssetLoader.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\LoadComplexItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\GridView\GridViewComplexDemoScript.cs" />
    <Compile Include="Assets\src\vo\battle\BattleVO.cs" />
    <Compile Include="Assets\src\Addressable\AssetPreloader.cs" />
    <Compile Include="Assets\src\utils\RankUtils.cs" />
    <Compile Include="Assets\src\view\popupwin\loader\LoaderProgressWin.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseVerticalItemList.cs" />
    <Compile Include="Assets\src\controls\container\ToggleContent.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ButtonPanel\ButtonPanelMenuList.cs" />
    <Compile Include="Assets\src\proxy\ScreenProxy.cs" />
    <Compile Include="Assets\src\controls\button\ToggleButtonSelected.cs" />
    <Compile Include="Assets\src\mediator\server\ServerMediator.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshProFloatingText.cs" />
    <Compile Include="Assets\src\controls\tooltip\ToolTip.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ToggleRowColItemList.cs" />
    <Compile Include="Assets\src\controls\scrollPane\ItemData.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\TextDescRowColItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\DraggableView\DraggableViewFadeLeftToRightDemoScript.cs" />
    <Compile Include="Assets\src\utils\ToolTipUtils.cs" />
    <Compile Include="Assets\src\data\SoldierInfo.cs" />
    <Compile Include="Assets\src\view\popupwin\basic\AlertWindow_AlertBackground.cs" />
    <Compile Include="Assets\src\utils\TimeUtils.cs" />
    <Compile Include="Assets\src\command\PopUpWinCommand.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ListView\ListViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\ImageItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseVerticalLineItemList.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroEquipmentMediator.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\TreeView\TreeViewDemoScript.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\NestedGridViewTopBottomItem.cs" />
    <Compile Include="Assets\src\utils\BuildNameUtils.cs" />
    <Compile Include="Assets\src\manager\CursorManager.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\SliderComplexItem.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\Item\BaseVerticalLineItem.cs" />
    <Compile Include="Assets\src\mediator\popupwin\heroInfo\HeroBodyView.cs" />
    <Compile Include="Assets\src\controls\list\SimpleListItem.cs" />
    <Compile Include="Assets\src\events\PopupWinEvent.cs" />
    <Compile Include="Assets\src\proxy\ChargeShopProxy.cs" />
    <Compile Include="Assets\src\view\main\bottom\component\ChatPage.cs" />
    <Compile Include="Assets\SuperScrollView\Demo\Scripts\ViewDemo\ResponsiveView\ResponsiveViewRefreshLoadDemoScript.cs" />
    <Compile Include="Assets\src\view\main\bottom\soldierPage\SoldierPanel.cs" />
    <Compile Include="Assets\src\vo\Alcazarlist\ResourceListVO.cs" />
    <Compile Include="Assets\SuperScrollView\Scripts\StaggeredGridView\StaggeredGridItemPool.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\Resources\locale\login.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Roboto-Bold - License.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets\ChocDino\UIFX\Docs\OFL.txt" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Unity - OFL.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\ChocDino\UIFX\Docs\Third-Party Notices.txt" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Oswald-Bold - OFL.txt" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Roboto-Bold - AFL.txt" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Anton OFL.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\Resources\locale\scene.txt" />
    <None Include="Assets\Art\Shaders\ChromaKey.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\SDFFunctions.hlsl" />
    <None Include="Assets\src\view\main\top\component\CastleBuildImageButton_back.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\ChocDino\UIFX\Docs\Documentation.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\Resources\locale\popupwin.txt" />
    <None Include="Assets\Art\Shaders\EdgeFixUltimate.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\SuperScrollView\Scripts\Version 2.5.4.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\Art\Shaders\EdgeFix.shader" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\Resources\locale\proxy.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\Resources\locale\gameObject.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Bangers - OFL.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WebGLModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\WebGLSupport\Managed\UnityEngine.WebGLModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="websocket-sharp">
      <HintPath>Assets\Plugins\websocket-sharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Json">
      <HintPath>Assets\Plugins\System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets\Plugins\Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web">
      <HintPath>Assets\Plugins\System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets\Plugins\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\software\unity3d\6000.1.3f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PsdPlugin">
      <HintPath>Library\ScriptAssemblies\PsdPlugin.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Common.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Path.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Common.Path.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.IK.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.IK.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ResourceManager">
      <HintPath>Library\ScriptAssemblies\Unity.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.IK.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.2D.IK.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.2D.SpriteShape.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Aseprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Aseprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InternalAPIEditorBridge.001">
      <HintPath>Library\ScriptAssemblies\Unity.InternalAPIEditorBridge.001.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Animation.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Animation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library\ScriptAssemblies\PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.PixelPerfect.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.PixelPerfect.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InternalAPIEngineBridge.001">
      <HintPath>Library\ScriptAssemblies\Unity.InternalAPIEngineBridge.001.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Tilemap.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Aseprite.Common">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Aseprite.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Animation.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Animation.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Elringus.SpriteGlow.Runtime">
      <HintPath>Library\ScriptAssemblies\Elringus.SpriteGlow.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Profiling.Core">
      <HintPath>Library\ScriptAssemblies\Unity.Profiling.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Extras.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Tilemap.Extras.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Postprocessing.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Extras">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Tilemap.Extras.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Common.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Toolchain.Win-x86_64-Linux-x86_64">
      <HintPath>Library\ScriptAssemblies\Unity.Toolchain.Win-x86_64-Linux-x86_64.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.SpriteShape.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Sysroot.Linux_x86_64">
      <HintPath>Library\ScriptAssemblies\Unity.Sysroot.Linux_x86_64.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Psdimporter.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Psdimporter.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.PixelPerfect">
      <HintPath>Library\ScriptAssemblies\Unity.2D.PixelPerfect.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.SysrootPackage.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.SysrootPackage.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="DOTweenPro.Scripts.csproj" />
    <ProjectReference Include="ChocDino.UIFX.TMP.csproj" />
    <ProjectReference Include="UniTask.Linq.csproj" />
    <ProjectReference Include="ChocDino.UIFX.Demos.csproj" />
    <ProjectReference Include="UnityWebSocket.Runtime.csproj" />
    <ProjectReference Include="OSA.Utilities.Editor.csproj" />
    <ProjectReference Include="OSA.Core.Editor.csproj" />
    <ProjectReference Include="ChocDino.UIFX.csproj" />
    <ProjectReference Include="OSA.Utilities.csproj" />
    <ProjectReference Include="OSA.Demos.csproj" />
    <ProjectReference Include="UniTask.csproj" />
    <ProjectReference Include="UnityWebSocket.Editor.csproj" />
    <ProjectReference Include="UniTask.Addressables.csproj" />
    <ProjectReference Include="UniTask.DOTween.csproj" />
    <ProjectReference Include="OSA.Core.csproj" />
    <ProjectReference Include="ChocDino.UIFX.Editor.csproj" />
    <ProjectReference Include="ChocDino.UIFX.UITK.Editor.csproj" />
    <ProjectReference Include="UnityUIExtensions.csproj" />
    <ProjectReference Include="ChocDino.UIFX.UITK.csproj" />
    <ProjectReference Include="DOTween.Modules.csproj" />
    <ProjectReference Include="UnityUIExtensions.examples.csproj" />
    <ProjectReference Include="UniTask.TextMeshPro.csproj" />
    <ProjectReference Include="ChocDino.UIFX.TMP.Editor.csproj" />
    <ProjectReference Include="UnityUIExtensions.editor.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
