
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

public class EquipmentBorder : GoldBorderUILoader
{


   public EquipmentVO equipmentvo;

   public UnityEvent<EquipmentBorder> OnClick_EB = new();
   public UnityEvent<EquipmentBorder> OnEnter_EB = new();
   public UnityEvent<EquipmentBorder> OnExit_EB = new();

   protected override void Awake()
   {
      base.Awake();
   }

   public override void OnPointerClick(PointerEventData eventData)
   {
      if (!buttonMode)
      {
         return;
      }
      OnClick_EB?.Invoke(this);
   }

   public override void OnPointerEnter(PointerEventData eventData)
   {

      base.OnPointerEnter(eventData);
      OnEnter_EB?.Invoke(this);
   }

   public override void OnPointerExit(PointerEventData eventData)
   {
      base.OnPointerExit(eventData);
      OnExit_EB?.Invoke(this);
   }
}

