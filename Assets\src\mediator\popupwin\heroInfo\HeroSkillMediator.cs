using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Pb;
using SuperScrollView;
using UnityEngine;
using UnityEngine.EventSystems;

public class HeroSkillMediator
{
    public const string NAME = "HeroSkillMediator";
    private const string NORMAL = "normal";

    private const string KEEL = "keel";

    private const string MSG_GETHERO = "MSG_GETHERO";

    public const string SKILL_PIC = "S_0";
    private PopupWinFrame win;
    private List<SkillVO> normal_link = new();

    private List<EquipmentVO> keel_link = new();

    private var gbuBox:Array;
      
      
      
      private ObjectPropertyProxy objectPropertyProxy;

    private string now_type = "normal";

    private int hero_id;

    private var action_Obj:Object;
      
      private float enableAlpha = 0.5f;

    private List<int> has_skill = new();

    private CastleVO castle_vo;

    private PlayerVO playervo;

    private Dictionary<int, int> itemsMap = new();

    private EquipmentBorder now_click;

    private int action_id = 0;

    private EquipmentProxy equipmentProxy;

    private var reelInfo:Map;
      
      private var skillTM:Map;
      
      private int equip;

    private int equipIndex;


    public HeroSkillMediator(PopupWinFrame viewObject)
    {
        win = viewObject;
        init();
        initListener();
    }

    private void init()
    {
        actionBtn.label = LanguageManager.getScene("LABEL_TM_EQU");
        actionSP.visible = false;
        actionBtn.OnClick.AddListener((btn) =>
        {
            actionHandler();
        });
        objectPropertyProxy = ProxyManager.Instance.objectPropertyProxy;
        equipmentProxy.getHero();
        hideBtnHandler(win.token).Forget();
        // objectPropertyProxy.getObjectPropertyCallBack(PlayerIdUtils._playId, parseEquipmentList);
    }
    private async UniTaskVoid hideBtnHandler(CancellationToken token)
    {
        try
        {
            while (!token.IsCancellationRequested)
            {
                // 高效等待鼠标点击（无输入时不消耗性能,支持移动端）
                await UniTask.WaitUntil(() => Input.GetMouseButtonDown(0) || Input.touchCount > 0,
                    PlayerLoopTiming.Update,
                    token);
                // 等待一帧让UI系统更新
                await UniTask.Yield();

                // 检查是否点击在UI上
                var results = RaycastUI();
                if (results.Count == 0)
                {
                    continue;
                }

                // 处理所有点击到的UI元素
                bool foundEquipment = false;
                foreach (var result in results)
                {
                    // 检测EquipmentBorder组件
                    if (result.gameObject.TryGetComponent<EquipmentBorder>(out _))
                    {
                        // Debug.Log($"检测到装备边框: {result.gameObject.name}");
                        foundEquipment = true;
                        break; // 找到一个即可
                    }
                }
                if (!foundEquipment)
                {
                    // Debug.Log("点击在UI上，但不是EquipmentBorder");
                    actionSP.visible = false;
                }
                // 避免连续检测过于频繁
                await UniTask.DelayFrame(1, cancellationToken: token);
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消
            Debug.Log("DetectClicksAsync canceled");
        }
    }
    private List<RaycastResult> RaycastUI()
    {
        var eventData = new PointerEventData(win._eventSystem)
        {
            position = Input.mousePosition,
            pressPosition = Input.mousePosition
        };

        var results = new List<RaycastResult>();
        win._raycaster.Raycast(eventData, results);

        return results;
    }

    private void castle_skill_id_lists(object p_data)
    {
        int i = 0;
        if (p_data is not null and List<int>)
        {
            List<int> arr = p_data as List<int>;
            normal_link.Clear();
            normal_link = new();
            for (i = 0; i < arr.Count; i++)
            {
                if (SkillProxy.skillInfo_map.ContainsKey(arr[i]))
                {
                    normal_link.Add(SkillProxy.skillInfo_map[arr[i]]);
                }
            }
            if (now_type == NORMAL)
            {
                insertBoxDataByType(now_type);
            }
        }
    }

    private void insertBoxDataByType(string _type)
    {
        List<GameObjectVO> link = null;
        EquipmentVO temp_vo = null;
        int i = 0;
        int index = 0;
        int box_child_len = 0;
        int len = 0;
        int type_length = 0;
        SkillVO ary = null;
        int keelId = 0;
        int len_num = 0;
        int m = 0;
        EquipmentBorder temp_UILoader1 = null;
        int leave_len = 0;
        EquipmentBorder temp = null;
        int n = 0;
        if (item_box == null)
        {
            return;
        }
        if (skillTM == null && now_type == NORMAL)
        {
            return;
        }
        if (!string.IsNullOrEmpty(_type))
        {
            clearBox();
            skillScrollPane.SetListItemCount(0, false);
            skillScrollPane.RefreshAllShownItem();
            skillScrollPane.ResetInitState();
            now_type = _type;
            changeButtonEnable();
            link = getAryLinkByType();
            if (link != null)
            {
                if (link.Count < 20)
                {
                    len = 20;
                }
                else
                {
                    len = link.Count;
                }
                skillScrollPane.InitGridView(len, (gridView, _index, row, column) =>
                {
                    LoopGridViewItem item = gridView.NewListViewItem("EquipmentBorder");
                    var localUILoader = item.GetComponent<EquipmentBorder>();
                    if (_index < link.Count)
                    {
                        if (now_type == NORMAL)
                        {
                            localUILoader.source = ResPathManager.getSkillIcon(localUILoader.equipmentvo.typeId, ResPathManager.RES_SIZE_M);
                        }
                        else if (now_type == KEEL)
                        {
                            localUILoader.equipmentvo = link[_index] as EquipmentVO;
                            localUILoader.source = ResPathManager.getPropIcon(localUILoader.equipmentvo.typeId, ResPathManager.RES_SIZE_M);
                        }

                        
                        localUILoader.useHandCursor = true;
                        localUILoader.buttonMode = true;
                        ToolTipManager.getInstance().regTarget(localUILoader.gameObject, " ");
                        localUILoader.OnEnter_EB.AddListener((_eb) =>
                        {
                            GoodsInfoBindAsync.showTipHandler(localUILoader.gameObject, localUILoader.equipmentvo.targetId, NAME);
                        });
                        addBorderListener(localUILoader);
                    }
                    else
                    {
                        ResPathManager.getMenuIcon(SKILL_PIC, ResPathManager.RES_SIZE_M, (sprite) =>
                        {
                            localUILoader.source = sprite;
                        });
                    }
                    return item;
                });
            }
            skillScrollPane.source = item_box;
        }
    }

    private void clearBox()
    {
        EquipmentBorder border = null;
        int len = item_box.childCount;
        while (len > 0)
        {
            border = item_box.GetChild(0).GetComponent<EquipmentBorder>();
            ToolTipManager.getInstance().unRegTarget(border.gameObject);
            removeBorderListener(border);
            // border.equipmentvo = null;
            // border.source = null;
            // border = null;
            len--;
        }
        while (gbuBox.length > 0)
        {
            gbuBox.pop();
        }
    }

    private void changeButtonEnable()
    {
        normalSkillButton.alpha = enableAlpha;
        keelSkillButton.alpha = enableAlpha;
        switch (now_type)
        {
            case NORMAL:
                normalSkillButton.alpha = 1;
                break;
            case KEEL:
                keelSkillButton.alpha = 1;
                break;
        }
    }

    private List<GameObjectVO> getAryLinkByType()
    {
        List<GameObjectVO> return_ary = null;
        switch (now_type)
        {
            case NORMAL:
                return_ary = normal_link.ConvertAll(item => item as GameObjectVO);
                break;
            case KEEL:
                return_ary = keel_link.ConvertAll(item => item as GameObjectVO);
                break;
        }
        return return_ary;
    }


    private void praseHeroVO(object p_data)
    {
        HeroVO hero_vo = null;
        if (p_data is HeroVO)
        {
            hero_vo = p_data as HeroVO;

        }
        else if (p_data is List<GameObjectVO>)
        {
            hero_vo = (p_data as List<GameObjectVO>)[0] as HeroVO;
        }
        else
        {
            return;
        }
        if (hero_vo.targetId == hero_id)
        {
            has_skill = new();
            if (hero_vo.skills != null)
            {
                has_skill = hero_vo.skills;
            }
        }
    }

    private void parseSkill(List<GameObjectVO> p_data)
    {
        if (p_data != null)
        {
            castle_vo = new CastleVO();
            castle_vo = MainModelLocator.getInstance().currentCastleData;
            if (castle_vo.heroSkills != null)
            {
            }
        }
    }

    private void parsePlayerEquipment(object p_data)
    {
        List<object> data = p_data as List<object>;
        int sort = (int)data[0];
        switch (sort)
        {
            case 5:
                keel_link = new();
                break;
            default:
                return;
        }
        List<EquipmentSimple> equipment = data[1] as List<EquipmentSimple>;
        EquipmentVO temp_vo = null;
        foreach (var item in equipment)
        {
            temp_vo = new()
            {
                targetId = item.TargetId,
                part = GoodsProxy.goodInfo_map[item.TypeId].part
            };
            temp_vo.targetId = item.TargetId;
            sortByType(temp_vo);
        }
    }


    public void handleNotification(string messageType, object data, string eventType)
    {
        switch (messageType)
        {
            case ObjectPropertyProxy.MSG_OBJECT_UPDATE_CASTLE:
                parseSkill(data as List<GameObjectVO>);
                break;
            case PopupWinEvent.HEROLOOKWINMEDIATOR_DATA_READY:
                if (data != null)
                {
                    hero_id = (int)data;
                    objectPropertyProxy.getObjectPropertyCallBack(hero_id, praseHeroVO);
                    if (hero_id > 0)
                    {
                        objectPropertyProxy.getObjectPropertyCallBack(CurrentCastleIdUtils.castleId, parseSkill);
                    }
                }
                break;
            case ObjectPropertyProxy.MSG_OBJECT_UPDATE_HERO:
                praseHeroVO(data);
                break;
            case ObjectPropertyProxy.MSG_OBJECT_UPDATE_PLAYER:
                parseEquipmentList(note.getBody());
                break;
            case PopupWinEvent.GET_REEL_NUM:
                MessageBus.Publish(PopupWinEvent.FOR_REEL_NUM, reelInfo);
                break;
            case EquipmentProxy.MSG_GETHERO:
                castle_skill_id_lists(data);
                break;
            case GoodsProxy.MSG_LOAD_PLAYER_EQUIPMENT:
                parsePlayerEquipment(data);
                break;

        }
    }



    private CommonButton normalSkillButton
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/skillBorderPanel/normalSkillButton").GetComponent<CommonButton>();
        }
    }
    private CommonButton keelSkillButton
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/skillBorderPanel/keelSkillButton").GetComponent<CommonButton>();
        }
    }

    private LoopGridView skillScrollPane
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/skillBorderPanel/skillScrollPane/Scroll View").GetComponent<LoopGridView>();
        }
    }

    private Transform item_box
    {
        get
        {
            return win.FindObjectByPath("win/contentPanel/herogoldBorderPanel/skillBorderPanel/skillScrollPane/Scroll View/Viewport/Content").transform;
        }
    }

    private ScaleSprite actionSP
    {
        get
        {
            return win.FindObjectByPath("win/actionLayer/actionSP").GetComponent<ScaleSprite>();
        }
    }

    private SmallButton actionBtn
    {
        get
        {
            return win.FindObjectByPath("win/actionLayer/actionSP/actionBtn").GetComponent<SmallButton>();
        }
    }

}