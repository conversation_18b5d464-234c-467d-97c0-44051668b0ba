using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

public class HeroEquipmentView : ScaleSprite
{
    public const string HELM_PIC = "E_1";

    public const string ARMOR_PIC = "E_2";

    public const string GLOVE_PIC = "E_3";

    public const string SHOE_PIC = "E_4";

    public const string WEAPON_PIC = "E_5";

    public const string NECKLACE_PIC = "E_6";

    public const string RING_PIC = "E_7";

    public const string MASK_PIC = "E_8";

    public const string BANGLE_PIC = "E_9";

    public const string HONOR_PIC = "E_10";

    public const string MAGICCRYSTAL_PIC = "E_11";

    public const string MAGICARMATURE_PIC = "E_12";

    public const string GIRDLE_PIC = "E_13";

    public const string AMULET_PIC = "E_14";

    public const string CLICK_TYPE = "EQUIPMENT";

    private ScaleSprite equipment_main_box;
    private HeroVO hero_vo;
    private List<EquipmentVO> itemHeroSuitEquipment;

    public HeroBodyView heroBody;

    private List<GoldBorderUILoader> equipment_border_list;

    public bool editable = false;

    private List<int> have_ary;

    private List<int> have_link;
    // 
    private ObjectPropertyProxy objectPropertyProxy;

    private List<EquipmentVO> equipment_vo_list;

    private int action_id = 0;

    public int action_index = -1;

    private bool once = false;

    private int new_index = -1;

    private GoldBorderUILoader new_link;

    private int count = 0;

    private int EquType = 1;

    // private KeyUtils key;

    private object obj_win;

    private string cantForgetIDS = "";

    public EquBtn equleft;
    public EquBtn equright;

    public GoldBorderUILoader helmGoldBorderUILoader;
    public GoldBorderUILoader armorGoldBorderUILoader;
    public GoldBorderUILoader gloveGoldBorderUILoader;
    public GoldBorderUILoader shoeGoldBorderUILoader;
    public GoldBorderUILoader weaponGoldBorderUILoader;
    public GoldBorderUILoader necklaceGoldBorderUILoader;
    public GoldBorderUILoader ring1GoldBorderUILoader;
    public GoldBorderUILoader ring2GoldBorderUILoader;

    private ChatProxy chatproxy;

    protected override void Awake()
    {
        base.Awake();
        objectPropertyProxy = ProxyManager.Instance.objectPropertyProxy;
        chatproxy = ProxyManager.Instance.chatProxy;
        equipment_border_list = new();
        for (int i = 0; i < 8; i++)
        {
            equipment_border_list.Add(null);
        }
    }

    private void Start()
    {
        creatUI();
    }

    private void creatUI()
    {
        equleft.switchState(3);
        equleft.CLICK.AddListener((btn) =>
        {
            switchEqu(btn);
        });
        equright.CLICK.AddListener((btn) =>
        {
            switchEqu(btn);
        });
        EquBtn.AddGroup(equleft);
        EquBtn.AddGroup(equright);
        ToolTipManager.getInstance().regTarget(helmGoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_HELMET"));
        equipment_border_list[2] = helmGoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(armorGoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_CLOTHE"));
        equipment_border_list[1] = armorGoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(gloveGoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_HAND"));
        equipment_border_list[4] = gloveGoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(shoeGoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_BOOTS"));
        equipment_border_list[3] = shoeGoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(weaponGoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_WEAPON"));
        equipment_border_list[0] = weaponGoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(necklaceGoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_NECKLACE"));
        equipment_border_list[7] = necklaceGoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(ring1GoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_RING"));
        equipment_border_list[5] = ring1GoldBorderUILoader;
        ToolTipManager.getInstance().regTarget(ring2GoldBorderUILoader.gameObject, LanguageManager.getScene("LABEL_TM_RING"));
        equipment_border_list[6] = ring2GoldBorderUILoader;
        heroBody.btn.OnClick.AddListener((btn) =>
        {
            manageOpenHandler();
        });
    }

    private void manageOpenHandler()
    {
        MessageBus.Publish(ApplicationFacade.EVENT_OPEN_WIN, new List<object> { hero_vo.targetId }, ModuleUtils.POPUPWIN_FEATURE_HERO);
    }

    public void switchEqu(EquBtn equBtn)
    {
        int i = 0;
        EquType = equBtn.EquType;
        int num = EquType == 1 ? 0 : 8;
        if (equipment_vo_list != null)
        {
            for (i = 0 + num; i < equipment_vo_list.Count / (2 / EquType); i++)
            {
                setEquipment(i, equipment_vo_list[i]).Forget();
            }
        }
    }

    private async UniTaskVoid setEquipment(int _index, EquipmentVO _vo)
    {
        GoldBorderUILoader temp = null;
        List<string> temp_ary = null;
        if (equipment_border_list[getRealIndex(_index)] != null)
        {
            temp = equipment_border_list[getRealIndex(_index)];
            // temp.filters = null;
            if (new_index == _index)
            {
                count = 0;
                new_index = -1;
                new_link = temp;
                // attackFilterRunBack();
            }
            if (_vo == null)
            {
                temp_ary = getIconByIndex(_index);
                temp.source = await ResPathManager.getMenuIcon(temp_ary[0]);
                temp.buttonMode = false;
                temp.useHandCursor = false;
                ToolTipManager.getInstance().regTarget(temp.gameObject, temp_ary[1]);
                temp.OnClick_UI.RemoveListener((sp) =>
                {
                    clickHandler(sp);
                });
            }
            else
            {
                temp.source = ResPathManager.getPropIcon(_vo.typeId, ResPathManager.RES_SIZE_M);
                temp.buttonMode = true;
                if (editable)
                {
                    temp.useHandCursor = true;
                }
                else
                {
                    temp.useHandCursor = false;
                }
                addBorderListener(temp);
                ToolTipManager.getInstance().regTarget(temp.gameObject, getToolTipContent(_vo));
            }
        }
    }

    private void addBorderListener(GoldBorderUILoader _sp)
    {
        _sp.OnClick_UI.AddListener((sp) =>
        {
            clickHandler(sp);
        });
    }

    private void clickHandler(GoldBorderUILoader evt)
    {
        int i = 0;
        EquipmentVO vo = null;
        // if (key.isDown(Keyboard.CONTROL))
        // {
        //     for (i = 0; i < equipment_border_list.Count; i++)
        //     {
        //         if (evt == equipment_border_list[i])
        //         {
        //             vo = equipment_vo_list[getConvertIndex(i)];
        //             chatproxy.equipmentShowChat(vo);
        //         }
        //     }
        // }
        // if (checkID(evt))
        // {
        //     ToolTipManager.getInstance().removeToolTip();
        //     if (editable)
        //     {
        //         dispatchEvent(new Event(CLICK_TYPE));
        //     }
        // }
    }

    private bool checkID(GoldBorderUILoader _target)
    {
        int id = 0;
        int i = 0;
        EquipmentVO vo = null;
        action_id = 0;
        bool b = false;
        for (i = 0; i < equipment_border_list.Count; i++)
        {
            if (_target == equipment_border_list[i])
            {
                vo = equipment_vo_list[getConvertIndex(i)];
                if (editable)
                {
                    action_id = vo.typeId;
                    action_index = getConvertIndex(i);
                    b = true;
                }
                break;
            }
        }
        return b;
    }

    private int getConvertIndex(int index)
    {
        if (EquType == 1)
        {
            return index;
        }
        return index + 8;
    }

    private string getToolTipContent(EquipmentVO vo)
    {
        return ToolTipUtils.equipmentTip(vo, itemHeroSuitEquipment);
    }

    private List<string> getIconByIndex(int _index)
    {
        string return_str = "";
        string return_str1 = "";
        switch (_index)
        {
            case 0:
                return_str = WEAPON_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_WEAPON");
                break;
            case 1:
                return_str = ARMOR_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_CLOTHE");
                break;
            case 2:
                return_str = HELM_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_HELMET");
                break;
            case 3:
                return_str = SHOE_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_BOOTS");
                break;
            case 4:
                return_str = GLOVE_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_HAND");
                break;
            case 5:
                return_str = RING_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_RING");
                break;
            case 6:
                return_str = RING_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_RING");
                break;
            case 7:
                return_str = NECKLACE_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_NECKLACE");
                break;
            case 8:
                return_str = MASK_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_MASK");
                break;
            case 9:
                return_str = BANGLE_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_BANGLE");
                break;
            case 10:
                return_str = AMULET_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_AMULET");
                break;
            case 11:
                return_str = HONOR_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_MEDAL");
                break;
            case 12:
                return_str = MAGICCRYSTAL_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_MAGICCRYSTAL");
                break;
            case 13:
                return_str = MAGICCRYSTAL_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_MAGICCRYSTAL");
                break;
            case 14:
                return_str = MAGICARMATURE_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_MAGICARMATURE");
                break;
            case 15:
                return_str = GIRDLE_PIC;
                return_str1 = LanguageManager.getScene("LABEL_TM_GIRDLE");
                break;
        }
        return new List<string>() { return_str, return_str1 };
    }

    private int getRealIndex(int index)
    {
        if (index < 8)
        {
            return index;
        }
        return index - 8;
    }

    public void setHeroVO(HeroVO _vo)
    {
        int i = 0;
        if (_vo != null)
        {
            if (hero_vo != null)
            {
                if (hero_vo.targetId == _vo.targetId && hero_vo.race == _vo.race && hero_vo.sex == _vo.sex && hero_vo.image == _vo.image)
                {
                    for (i = 0; i < hero_vo.items.Count; i++)
                    {
                        if (_vo.items[i] != hero_vo.items[i])
                        {
                            if (_vo.items[i] > 0)
                            {
                                new_index = i;
                            }
                            break;
                        }
                    }
                    if (i == hero_vo.items.Count)
                    {
                        return;
                    }
                }
            }
            hero_vo = _vo;
            heroBody.setHeroVO(hero_vo);
            parseHeroItems();
        }
    }

    private void parseHeroItems()
    {
        int i = 0;
        if (hero_vo.items != null && hero_vo.items.Count > 0)
        {
            have_ary = new();
            have_link = new();
            for (i = 0; i < 16; i++)
            {
                if (hero_vo.items[i] != 0)
                {
                    have_ary.Add(hero_vo.items[i]);
                    have_link.Add(i);
                }
            }
            if (have_ary.Count == 0)
            {
                parseEquipmentVO(new List<GameObjectVO>());
            }
            else
            {
                objectPropertyProxy.getQueueByIdList(have_ary, parseEquipmentVO, ObjectPropertyProxy.GAMEOBJECT_TYPE_CLASS, true);
            }
        }
    }

    private void parseEquipmentVO(List<GameObjectVO> p_data)
    {
        List<EquipmentVO> ary = null;
        int i = 0;
        int num = 0;
        if (p_data != null)
        {
            ary = p_data.ConvertAll(item => item as EquipmentVO).FindAll(item => item != null);
            equipment_vo_list = new();
            for (i = 0; i < 16; i++)
            {
                equipment_vo_list.Add(null);
            }
            i = 0;
            if (have_link.Count != ary.Count)
            {
                return;
            }
            // 将arr集合前8个元素赋值给itemHeroSuitEquipment, 未达到8个则全部赋值
            itemHeroSuitEquipment = ary.GetRange(0, Math.Min(8, ary.Count)); // 未达到8个则全部赋值
            for (i = 0; i < have_link.Count; i++)
            {
                equipment_vo_list[have_link[i]] = ary[i];
            }
            // TweenLite.killTweensOf(new_link);
            num = EquType == 1 ? 0 : 8;
            for (i = 0 + num; i < equipment_vo_list.Count / (2 / EquType); i++)
            {
                setEquipment(i, equipment_vo_list[i]);
            }
        }
        if (hero_vo.ownerId == MainModelLocator.getInstance().playerInfo.targetId)
        {
            heroBody.showBtn(true, "");
        }
        else
        {
            heroBody.showBtn(false, setFeatureTips(hero_vo.passiveFeature, FeatureProxy.heroFeatureArray) + setFeatureTips(hero_vo.learnFeature, FeatureProxy.learnFeatureArray) + setFeatureTips(hero_vo.battleFeature, FeatureProxy.battleFeatureArray));
        }
    }

    private string setFeatureTips(List<int> arr, List<HeroFeatureItem> featureArray)
    {
        string str = "";
        int i = 0;
        int j = 0;
        if (featureArray != null && arr != null && arr.Count > 0 && featureArray.Count > 0)
        {
            for (i = 0; i < arr.Count; i++)
            {
                for (j = 0; j < featureArray.Count; j++)
                {
                    if (featureArray[j].typeId.Contains(arr[i]))
                    {
                        str += FeatureTools.showFeatureTip(featureArray[j], arr[i]);
                    }
                }
            }
        }
        return str;
    }

    public void onRemove()
    {

    }
}