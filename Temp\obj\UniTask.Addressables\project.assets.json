{"version": 3, "targets": {".NETStandard,Version=v2.1": {"UniTask/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UniTask.dll": {}}, "runtime": {"bin/placeholder/UniTask.dll": {}}}}}, "libraries": {"UniTask/1.0.0": {"type": "project", "path": "UniTask.csproj", "msbuildProject": "UniTask.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["UniTask >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.Addressables.csproj", "projectName": "UniTask.Addressables", "projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.Addressables.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\Temp\\obj\\UniTask.Addressables\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.csproj": {"projectPath": "D:\\unity\\HeroCastleRemake-Unity6.1\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.202\\RuntimeIdentifierGraph.json"}}}}