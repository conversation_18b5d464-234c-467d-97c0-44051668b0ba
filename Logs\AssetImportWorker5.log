Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.3f1 (f34db9734971) revision 15945145'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 65299 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-28T09:53:30Z

COMMAND LINE ARGUMENTS:
D:\software\unity3d\6000.1.3f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
D:/unity/HeroCastleRemake-Unity6.1
-logFile
Logs/AssetImportWorker5.log
-srvPort
9112
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/unity/HeroCastleRemake-Unity6.1
D:/unity/HeroCastleRemake-Unity6.1
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [62332]  Target information:

Player connection [62332]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2951399125 [EditorId] 2951399125 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62332]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 2951399125 [EditorId] 2951399125 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62332]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2951399125 [EditorId] 2951399125 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62332]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2951399125 [EditorId] 2951399125 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62332]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 2951399125 [EditorId] 2951399125 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62332]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2951399125 [EditorId] 2951399125 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62332] Host joined multi-casting on [***********:54997]...
Player connection [62332] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 5.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.3f1 (f34db9734971)
[Subsystems] Discovering subsystems at path D:/software/unity3d/6000.1.3f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/unity/HeroCastleRemake-Unity6.1/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'D:/software/unity3d/6000.1.3f1/Editor/Data/Managed'
Mono path[1] = 'D:/software/unity3d/6000.1.3f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/software/unity3d/6000.1.3f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56232
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.011921 seconds.
- Loaded All Assemblies, in  0.373 seconds
Native extension for LinuxStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for AppleTV target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 5535 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.906 seconds
Domain Reload Profiling: 6271ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (59ms)
	LoadAllAssembliesAndSetupDomain (148ms)
		LoadAssemblies (111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (142ms)
			TypeCache.Refresh (141ms)
				TypeCache.ScanAssembly (130ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (5907ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5864ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5670ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (45ms)
			ProcessInitializeOnLoadAttributes (100ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.018 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Native extension for LinuxStandalone target not found
Native extension for AppleTV target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
System.Exception: Error! Visual Scripting: couldn't find visual scripting package
  at Unity.VisualScripting.PackageVersionUtility.get_version () [0x00043] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\PackageVersionUtility.cs:31 
  at Unity.VisualScripting.BoltCoreManifest.get_version () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugin\BoltCoreManifest.cs:11 
  at Unity.VisualScripting.PluginManifest.get_currentVersion () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginManifest.cs:33 
  at Unity.VisualScripting.PluginManifest.get_versionMismatch () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginManifest.cs:48 
  at Unity.VisualScripting.PluginContainer.Initialize () [0x00525] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginContainer.cs:208 
  at Unity.VisualScripting.PluginContainer.InitializeOnLoad () [0x00009] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginContainer.cs:32 
  at Unity.VisualScripting.VSUsageUtility.DoInitializeOnLoadCalls () [0x00034] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:76 
  at Unity.VisualScripting.VSUsageUtility.set_isVisualScriptingUsed (System.Boolean value) [0x0002c] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:114 
  at Unity.VisualScripting.VSUsageUtility.CheckAndSetIsVisualScriptingUsed () [0x00032] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:48 
  at Unity.VisualScripting.VSUsageUtility..cctor () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:22 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.124 seconds
Domain Reload Profiling: 2119ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (733ms)
		LoadAssemblies (525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (309ms)
			TypeCache.Refresh (226ms)
				TypeCache.ScanAssembly (203ms)
			BuildScriptInfoCaches (63ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1124ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1023ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (812ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 4.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 287 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8638 unused Assets / (9.7 MB). Loaded Objects now: 9292.
Memory consumption went from 219.8 MB to 210.0 MB.
Total: 23.747900 ms (FindLiveObjects: 0.755200 ms CreateObjectMapping: 0.517200 ms MarkObjects: 17.569100 ms  DeleteObjects: 4.904700 ms)

========================================================================
Received Import Request.
  Time since last request: 22990.926128 seconds.
  path: Assets/Art/Textures/UI/equipment/36005_M.png
  artifactKey: Guid(04010b5ee2f761e4791f40a690750099) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36005_M.png using Guid(04010b5ee2f761e4791f40a690750099) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dee0ad24a9072ed9ba4c86799cc036bf') in 1.5646458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Art/Textures/UI/equipment/36020_M.png
  artifactKey: Guid(06920c4db3b3a9c44803b8539021f8af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36020_M.png using Guid(06920c4db3b3a9c44803b8539021f8af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80cfee1e6ac0d78adc71d718a48825a3') in 1.3167283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Art/Textures/UI/equipment/36029_M.png
  artifactKey: Guid(3f099457f072e0049bab4d96a5acb409) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36029_M.png using Guid(3f099457f072e0049bab4d96a5acb409) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f13284ff31a8a7ca63768d1f64e1d469') in 1.3208789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Art/Textures/UI/equipment/36041_M.png
  artifactKey: Guid(f79ca11e174ea5e489fe84f2dae84786) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36041_M.png using Guid(f79ca11e174ea5e489fe84f2dae84786) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '808b3d26a2b3690240904a9c1bd7eace') in 1.3334604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Art/Textures/UI/equipment/36013_M.png
  artifactKey: Guid(ffe1a4ce5e58c0349a7e1028404e2ca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36013_M.png using Guid(ffe1a4ce5e58c0349a7e1028404e2ca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f93efd52d8717a3892ab6e7eff47143') in 0.9943276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0