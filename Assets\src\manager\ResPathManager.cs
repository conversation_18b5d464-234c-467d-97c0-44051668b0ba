
using System;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.U2D;

public class ResPathManager : MonoBehaviour
{

    public const string RES_TYPE_0 = "0";

    public const string RES_TYPE_1 = "1";

    public const string RES_TYPE_2 = "2";

    public const string RES_TYPE_3 = "3";

    public const string RES_TYPE_4 = "4";

    public const string RES_TYPE_5 = "5";

    public const string RES_TYPE_6 = "6";

    public const string RES_TYPE_7 = "7";

    public const string RES_TYPE_8 = "8";

    public const string RES_TYPE_9 = "9";

    public const string RES_TYPE_10 = "10";

    public const string RES_SIZE_L = "L";

    public const string RES_SIZE_M = "M";

    public const string RES_SIZE_S = "S";

    public const string RES_STATE_COMPLETE = "1";

    public const string RES_STATE_BUILDING = "2";

    public const string RES_TYPE_WOOD = "11001";

    public const string RES_TYPE_ORE = "11002";

    public const string RES_TYPE_SULFUR = "11003";

    public const string RES_TYPE_CRYSTAL = "11004";

    public const string RES_TYPE_GOLD = "11005";

    public const string RES_TYPE_EXP = "11006";

    public const string RES_TYPE_MINE_WOOD = "12001";

    public const string RES_TYPE_MINE_ORE = "12002";

    public const string RES_TYPE_MINE_SULFUR = "12003";

    public const string RES_TYPE_MINE_CRYSTAL = "12004";

    public const string RES_TYPE_MINE_GOLD = "12005";

    public const string RES_ICON_GOLD = "money";

    public const string RES_ICON_LOAD = "space";

    public const string PROPERTY_ATTACK = "attack";

    public const string PROPERTY_DEFENCE = "defence";

    public const string PROPERTY_KNOWLEDGE = "knowledge";

    public const string PROPERTY_SPEED = "speed";

    public const string RES_PLAYER_HEAD = "Atlas/PlayerHeadAtlas";
    public const string RES_CASTLE_IMAGE = "Atlas/CastleImageAtlas";
    public const string RES_DIR_HERO_HEAD = "Atlas/HeroHeadAtlas";
    public const string RES_PROP_ICON_ATLAS = "Assets/Game/Atlas/EquipmentAtlas.spriteatlasv2";
    public const string RES_SKILL_ICON_ATLAS = "Assets/Game/Atlas/Skill/SkillIconAtlas.spriteatlasv2";
    public const string RES_MENU_ICON_ATLAS = "Assets/Game/Atlas/MenuAtlas.spriteatlasv2";
    public const string RES_HERO_BODY_ATLAS_DIR = "Assets/Game/Atlas/Hero/B/";


    public const string RES_SOLDIERS_DATA = "Assets/Game/Data/soldiers.json";
    public const string RES_EQUIPMENT_DATA = "Assets/Game/Data/equipment.json";
    public const string RES_SKILL_DATA = "Assets/Game/Data/skill.json";
    public const string RES_BUILD_DATA = "Assets/Game/Data/build.json";
    public const string RES_UNUSEDITEMS_DATA = "Assets/Game/Data/unUsedItems.json";
    public const string RES_PRESENT_DATA = "Assets/Game/Data/present.json";
    public const string RES_FEATURE_DATA = "Assets/Game/Data/feature.json";


    private static SpriteAtlas playerHeadAtlas;

    private static SpriteAtlas castleImageAtlas;
    private static SpriteAtlas raceSoldierIconAtlas;
    private static SpriteAtlas heroHeadIconAtlas;
    public static SpriteAtlas propIconAtlas;
    public static SpriteAtlas skillIconAtlas;
    public static SpriteAtlas menuAtlas;

    public SpriteAtlas mainUIAtlas;

    public static ResPathManager instance;


    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    public static void LoadPlayerHeadAtlas(Action onComplete)
    {
        if (playerHeadAtlas != null)
        {
            onComplete?.Invoke();
            return;
        }

        ResourceRequest request = Resources.LoadAsync<SpriteAtlas>(RES_PLAYER_HEAD);
        request.completed += (AsyncOperation op) =>
        {
            ResourceRequest resourceRequest = op as ResourceRequest;
            if (resourceRequest != null && resourceRequest.asset != null)
            {
                Debug.Log("PlayerHeadAtlas loaded successfully");
                playerHeadAtlas = resourceRequest.asset as SpriteAtlas;
                onComplete?.Invoke();
            }
            else
            {
                Debug.LogError("Failed to load PlayerHeadAtlas");
                onComplete?.Invoke();
            }
        };
    }


    public static Sprite GetPlayerHeadImage(int p_race, int p_sex)
    {
        if (playerHeadAtlas == null)
        {
            playerHeadAtlas = Resources.Load<SpriteAtlas>(RES_PLAYER_HEAD);
            // Debug.LogError("PlayerHeadAtlas is not loaded!");
            // return null;
        }
        string imgStr = "";
        switch (p_race)
        {
            case GameObjectUtils.GAME_RACE_HUMAN:
                if (p_sex == 1)
                {
                    imgStr = "56001";
                }
                else
                {
                    imgStr = "56002";
                }
                break;
            case GameObjectUtils.GAME_RACE_FAIRY:
                if (p_sex == 1)
                {
                    imgStr = "56003";
                }
                else
                {
                    imgStr = "56004";
                }
                break;
            case GameObjectUtils.GAME_RACE_GHOST:
                if (p_sex == 1)
                {
                    imgStr = "56005";
                }
                else
                {
                    imgStr = "56006";
                }
                break;
            case GameObjectUtils.GAME_RACE_BARBARIAN:
                if (p_sex == 1)
                {
                    imgStr = "56007";
                }
                else
                {
                    imgStr = "56008";
                }
                break;
        }
        return playerHeadAtlas.GetSprite(imgStr);
    }

    public static Sprite GetCastleImage(int p_typeId, int p_born, string p_resSize)
    {
        if (castleImageAtlas == null)
        {
            castleImageAtlas = Resources.Load<SpriteAtlas>(RES_CASTLE_IMAGE);
        }
        string imgStr = p_typeId + "_" + p_born + "_" + p_resSize;
        return castleImageAtlas.GetSprite(imgStr);
    }

    public static TextAsset GetSoldiersData()
    {
        return Resources.Load<TextAsset>(RES_SOLDIERS_DATA);
    }

    public static TextAsset GetFeatureData()
    {
        return Resources.Load<TextAsset>(RES_FEATURE_DATA);
    }

    public static TextAsset GetBuildData()
    {
        return Resources.Load<TextAsset>(RES_BUILD_DATA);
    }

    public static TextAsset GetPropData()
    {
        return Resources.Load<TextAsset>(RES_EQUIPMENT_DATA);
    }

    public static TextAsset GetSkillData()
    {
        return Resources.Load<TextAsset>(RES_SKILL_DATA);
    }

    public static TextAsset GetUnUsedItemsData()
    {
        return Resources.Load<TextAsset>(RES_UNUSEDITEMS_DATA);
    }

    public static TextAsset GetPresentData()
    {
        return Resources.Load<TextAsset>(RES_PRESENT_DATA);
    }

    public static Sprite getRaceSoldierIcon(int p_raceId, int p_typeId, string p_size)
    {
        if (raceSoldierIconAtlas == null)
        {
            raceSoldierIconAtlas = Resources.Load<SpriteAtlas>("Atlas/RaceSoldierIconAtlas");
        }
        return raceSoldierIconAtlas.GetSprite(p_typeId + "_" + p_size);
    }

    public static Sprite getHeroHeadImage(int p_headId, string p_size)
    {
        if (heroHeadIconAtlas == null)
        {
            heroHeadIconAtlas = Resources.Load<SpriteAtlas>(RES_DIR_HERO_HEAD);
        }
        return heroHeadIconAtlas.GetSprite(p_headId + "_" + p_size);
    }
    public static Sprite getHeroHeadImage(string frame)
    {
        if (heroHeadIconAtlas == null)
        {
            heroHeadIconAtlas = Resources.Load<SpriteAtlas>(RES_DIR_HERO_HEAD);
        }
        return heroHeadIconAtlas.GetSprite(frame);
    }

    public static string getHeroBodyAtlasPath(int image)
    {
        return RES_HERO_BODY_ATLAS_DIR + image + "_B.spriteatlasv2";
    }

    public static Sprite getPropIcon(int p_typeId, string p_size)
    {
        return propIconAtlas.GetSprite(p_typeId + "_" + p_size);
    }

    public static async UniTask<Sprite> getSkillIcon(int p_skillId, string p_size, Action<Sprite> onComplete = null)
    {
        if (skillIconAtlas == null)
        {
            skillIconAtlas = await AddressableManager.Instance.LoadAssetAsync<SpriteAtlas>(RES_SKILL_ICON_ATLAS);
        }
        if (skillIconAtlas != null)
        {
            onComplete?.Invoke(skillIconAtlas.GetSprite(p_skillId + "_" + p_size));
        }
        else
        {
            Debug.LogError("Failed to load skill icon atlas.");
        }
        return skillIconAtlas.GetSprite(p_skillId + "_" + p_size);
    }

    public static async UniTask<Sprite> getMenuIcon(string resType, string p_resSize = "", Action<Sprite> onComplete = null)
    {
        string resName = "";
        if (p_resSize != "")
        {
            resName = resType + "_" + p_resSize;
        }
        else
        {
            resName = resType;
        }
        if (menuAtlas == null)
        {
            menuAtlas = await AddressableManager.Instance.LoadAssetAsync<SpriteAtlas>(RES_MENU_ICON_ATLAS);
        }
        if (menuAtlas != null)
        {
            onComplete?.Invoke(menuAtlas.GetSprite(resName));
        }
        else
        {
            Debug.LogError("Failed to load menu icon atlas.");
        }
        return menuAtlas.GetSprite(resName);
    }
}