Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.3f1 (f34db9734971) revision 15945145'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 65299 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-28T09:41:20Z

COMMAND LINE ARGUMENTS:
D:\software\unity3d\6000.1.3f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/unity/HeroCastleRemake-Unity6.1
-logFile
Logs/AssetImportWorker0.log
-srvPort
9112
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/unity/HeroCastleRemake-Unity6.1
D:/unity/HeroCastleRemake-Unity6.1
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [64680]  Target information:

Player connection [64680]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3027052690 [EditorId] 3027052690 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [64680]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 3027052690 [EditorId] 3027052690 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [64680]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3027052690 [EditorId] 3027052690 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [64680]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3027052690 [EditorId] 3027052690 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [64680]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3027052690 [EditorId] 3027052690 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [64680]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3027052690 [EditorId] 3027052690 [Version] 1048832 [Id] WindowsEditor(7,SpaceX) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [64680] Host joined multi-casting on [***********:54997]...
Player connection [64680] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.3f1 (f34db9734971)
[Subsystems] Discovering subsystems at path D:/software/unity3d/6000.1.3f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/unity/HeroCastleRemake-Unity6.1/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'D:/software/unity3d/6000.1.3f1/Editor/Data/Managed'
Mono path[1] = 'D:/software/unity3d/6000.1.3f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/software/unity3d/6000.1.3f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56532
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.007712 seconds.
- Loaded All Assemblies, in  0.361 seconds
Native extension for LinuxStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for AppleTV target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2502 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.860 seconds
Domain Reload Profiling: 3216ms
	BeginReloadAssembly (124ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (122ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (135ms)
			TypeCache.Refresh (133ms)
				TypeCache.ScanAssembly (123ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2860ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2817ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2630ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (43ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.890 seconds
Refreshing native plugins compatible for Editor in 2.61 ms, found 4 plugins.
Native extension for LinuxStandalone target not found
Native extension for AppleTV target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
System.Exception: Error! Visual Scripting: couldn't find visual scripting package
  at Unity.VisualScripting.PackageVersionUtility.get_version () [0x00043] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\PackageVersionUtility.cs:31 
  at Unity.VisualScripting.BoltCoreManifest.get_version () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugin\BoltCoreManifest.cs:11 
  at Unity.VisualScripting.PluginManifest.get_currentVersion () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginManifest.cs:33 
  at Unity.VisualScripting.PluginManifest.get_versionMismatch () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginManifest.cs:48 
  at Unity.VisualScripting.PluginContainer.Initialize () [0x00525] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginContainer.cs:208 
  at Unity.VisualScripting.PluginContainer.InitializeOnLoad () [0x00009] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Plugins\PluginContainer.cs:32 
  at Unity.VisualScripting.VSUsageUtility.DoInitializeOnLoadCalls () [0x00034] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:76 
  at Unity.VisualScripting.VSUsageUtility.set_isVisualScriptingUsed (System.Boolean value) [0x0002c] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:114 
  at Unity.VisualScripting.VSUsageUtility.CheckAndSetIsVisualScriptingUsed () [0x00032] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:48 
  at Unity.VisualScripting.VSUsageUtility..cctor () [0x00000] in .\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Utilities\VSUsageUtility.cs:22 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.042 seconds
Domain Reload Profiling: 1917ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (658ms)
		LoadAssemblies (459ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (286ms)
			TypeCache.Refresh (209ms)
				TypeCache.ScanAssembly (192ms)
			BuildScriptInfoCaches (57ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1042ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (940ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (733ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 287 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8638 unused Assets / (9.8 MB). Loaded Objects now: 9292.
Memory consumption went from 225.5 MB to 215.7 MB.
Total: 18.694100 ms (FindLiveObjects: 0.579000 ms CreateObjectMapping: 0.343000 ms MarkObjects: 14.427400 ms  DeleteObjects: 3.343400 ms)

========================================================================
Received Import Request.
  Time since last request: 22257.220240 seconds.
  path: Assets/Prefabs/Component/GoldBorderUILoader.prefab
  artifactKey: Guid(3b2cf7a562cebe34081e3a437cb05a6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderUILoader.prefab using Guid(3b2cf7a562cebe34081e3a437cb05a6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '622715ffa11b415d5cf954ae846a2f05') in 0.5225138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 77

========================================================================
Received Import Request.
  Time since last request: 9.708730 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b83eb9fc8fb866a072fc2fa2603c712') in 0.0030348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 1.094440 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '075b82728044900ec9f022aa5147241a') in 0.0027289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 63

========================================================================
Received Import Request.
  Time since last request: 1.861796 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '430e8e4f475b6261b62aa411a2647ceb') in 0.0027428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 2.070050 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ea874ae3f81f6930bf24f213ccc96d0') in 0.0026157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 3.443042 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a59f7160321de3e3513211e623aaf736') in 0.0026411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 4.816319 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc5392284eb31ef2fc6a83606a19783e') in 0.0026819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 6.245547 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e121b96fb22eddfdbff1c44b22ffe81') in 0.0048774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 74.432440 seconds.
  path: Assets/Prefabs/Component/GoldBorderPanel.prefab
  artifactKey: Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Component/GoldBorderPanel.prefab using Guid(4bfb07e255cf6694ab990eb53b50791e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ac4468f5f9f46238afb4ebd2f0748e70') in 0.0034551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 67

========================================================================
Received Import Request.
  Time since last request: 29.080920 seconds.
  path: Assets/Game/Prefabs/popupwin/heroInfo/EquipmentBorder.prefab
  artifactKey: Guid(493dab4f7e593f2448b0af1a2b5edcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Game/Prefabs/popupwin/heroInfo/EquipmentBorder.prefab using Guid(493dab4f7e593f2448b0af1a2b5edcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f0ac78efc43307b2c83e87d2b8991669') in 0.4095885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 81

========================================================================
Received Import Request.
  Time since last request: 5.997838 seconds.
  path: Assets/Prefabs/UI/HeroItemRender.prefab
  artifactKey: Guid(b021417a2b1707b45916a153952a66e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/UI/HeroItemRender.prefab using Guid(b021417a2b1707b45916a153952a66e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37eb30e5f926d655db90ee23ff46545d') in 0.4062632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 416

========================================================================
Received Import Request.
  Time since last request: 73.056588 seconds.
  path: Assets/Prefabs/UI/HeroItemRender.prefab
  artifactKey: Guid(b021417a2b1707b45916a153952a66e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/UI/HeroItemRender.prefab using Guid(b021417a2b1707b45916a153952a66e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2cb1b97574d442dd34725786c58956f7') in 0.4228076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 406

========================================================================
Received Import Request.
  Time since last request: 217.945474 seconds.
  path: Assets/Game/Prefabs/popupwin/heroInfo/EquipmentBorder.prefab
  artifactKey: Guid(493dab4f7e593f2448b0af1a2b5edcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Game/Prefabs/popupwin/heroInfo/EquipmentBorder.prefab using Guid(493dab4f7e593f2448b0af1a2b5edcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2f159efd38473fc15054469b0b8e0c6') in 0.4186356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 80

========================================================================
Received Import Request.
  Time since last request: 13.688789 seconds.
  path: Assets/Game/Prefabs/popupwin/heroInfo/EquipmentBorder.prefab
  artifactKey: Guid(493dab4f7e593f2448b0af1a2b5edcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Game/Prefabs/popupwin/heroInfo/EquipmentBorder.prefab using Guid(493dab4f7e593f2448b0af1a2b5edcdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '272138a5fc90a98bd504e79f785739dc') in 0.40086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 80

========================================================================
Received Import Request.
  Time since last request: 274.273398 seconds.
  path: Assets/Art/Textures/UI/equipment/36010_M.png
  artifactKey: Guid(a50593ddcf6a2004b9646453abd2a12b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36010_M.png using Guid(a50593ddcf6a2004b9646453abd2a12b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9931b22473d8060bba918b99428c30fc') in 0.9299483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.025608 seconds.
  path: Assets/Art/Textures/UI/equipment/36003_M.png
  artifactKey: Guid(53affdb96aaa8ef48b0765c7fd755f07) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36003_M.png using Guid(53affdb96aaa8ef48b0765c7fd755f07) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3836b1770edfb4dc0379b44f81856c93') in 0.8755933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Art/Textures/UI/equipment/36011_M.png
  artifactKey: Guid(62dc4236b12b9094ba1867e27523eff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36011_M.png using Guid(62dc4236b12b9094ba1867e27523eff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25b49b5c68b3ee8933c53e7bb2dba014') in 0.9089602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Art/Textures/UI/equipment/36032_M.png
  artifactKey: Guid(1acf187802be4e04588c7d02059d77a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36032_M.png using Guid(1acf187802be4e04588c7d02059d77a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa4abeb8ded78d20f32cc79b141938c3') in 0.8849981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Art/Textures/UI/equipment/36026_M.png
  artifactKey: Guid(72affdfd8bb71464ca1e858f77d59f05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36026_M.png using Guid(72affdfd8bb71464ca1e858f77d59f05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '03221d7e914a267b745a81a39ce1d5b5') in 0.8338617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Art/Textures/UI/equipment/36023_M.png
  artifactKey: Guid(2afef8cd3185b794db638d580d81b4b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36023_M.png using Guid(2afef8cd3185b794db638d580d81b4b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5a0ab0b7b33cd5d4b023194b12f19fa') in 0.7670367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Art/Textures/UI/equipment/36024_M.png
  artifactKey: Guid(2c4a5d72c52f365409a0a6a5dcea307c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36024_M.png using Guid(2c4a5d72c52f365409a0a6a5dcea307c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5471853c19699707ecca73405daebca7') in 0.786539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Art/Textures/UI/equipment/36004_M.png
  artifactKey: Guid(14dfb77ec120ff74ca1a2fd879ea811e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36004_M.png using Guid(14dfb77ec120ff74ca1a2fd879ea811e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '866fa34b52e76b220f2e6c1619829ee3') in 0.7716146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Art/Textures/UI/equipment/36006_M.png
  artifactKey: Guid(1a5fb710509117e41875f6490615b09e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36006_M.png using Guid(1a5fb710509117e41875f6490615b09e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c969f2ac1f6d3f535e283bcce49c68b1') in 0.8311271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Art/Textures/UI/equipment/36002_M.png
  artifactKey: Guid(40d3f74baa1db3443a2e3fa37cc77d0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36002_M.png using Guid(40d3f74baa1db3443a2e3fa37cc77d0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd98d2ad4c66f9e84356e448a787f82c2') in 0.9139607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Art/Textures/UI/equipment/36015_M.png
  artifactKey: Guid(ad7a90d5446154a4c952ce8cf24f0c4d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36015_M.png using Guid(ad7a90d5446154a4c952ce8cf24f0c4d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cbfedfdfd791b2fcbfddb9b01f921522') in 1.0118237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 2.923327 seconds.
  path: Assets/Art/Textures/UI/equipment/36002_M.png
  artifactKey: Guid(40d3f74baa1db3443a2e3fa37cc77d0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36002_M.png using Guid(40d3f74baa1db3443a2e3fa37cc77d0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c746f442f19bb8fbdb6a64186d5f19cd') in 1.3519703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Art/Textures/UI/equipment/36008_M.png
  artifactKey: Guid(abfbddf2f0e3c664e87c1a9e42a93e2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36008_M.png using Guid(abfbddf2f0e3c664e87c1a9e42a93e2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a0baa773956f81a8e9c8a66e726b813d') in 1.3000558 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Art/Textures/UI/equipment/36009_M.png
  artifactKey: Guid(9c8d7b58de4d72849ae78045ebb39508) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36009_M.png using Guid(9c8d7b58de4d72849ae78045ebb39508) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6974bace83f03304fd14b468e52ea69e') in 1.3163042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Art/Textures/UI/equipment/36019_M.png
  artifactKey: Guid(8c20771207377964b9bf0afd07c84f52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36019_M.png using Guid(8c20771207377964b9bf0afd07c84f52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53f9ca8fb7e1eff4f01f7dffd183cff4') in 1.3374907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Art/Textures/UI/equipment/36034_M.png
  artifactKey: Guid(f0d0a45321afca244bf8e6723b0d76f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Textures/UI/equipment/36034_M.png using Guid(f0d0a45321afca244bf8e6723b0d76f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07e90bb44cfcf9349615eac9f4fc2812') in 1.0622899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4875

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0