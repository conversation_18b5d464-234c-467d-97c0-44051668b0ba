-target:library
-out:"Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_1_3
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AUDIO
-define:ENABLE_CLOTH
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_WEBGL
-define:UNITY_WEBGL
-define:UNITY_WEBGL_API
-define:UNITY_DISABLE_WEB_VERIFICATION
-define:UNITY_GFX_USE_PLATFORM_VSYNC
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:LETAI_TRUESHADOW
-define:VUPLEX_CCU
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Google.Protobuf.dll"
-r:"Assets/Plugins/Microsoft.Bcl.AsyncInterfaces.dll"
-r:"Assets/Plugins/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/Plugins/System.Text.Encodings.Web.dll"
-r:"Assets/Plugins/System.Text.Json.dll"
-r:"Assets/Plugins/websocket-sharp.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.Apple.Extensions.Common.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/PlaybackEngines/WebGLSupport/Managed/UnityEngine.WebGLModule.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"D:/software/unity3d/6000.1.3f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.Demos.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.TMP.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.TMP.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.UITK.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/ChocDino.UIFX.UITK.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/DOTween.Modules.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/DOTweenPro.Scripts.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Elringus.SpriteGlow.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/OSA.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/OSA.Core.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/OSA.Demos.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/OSA.Utilities.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/OSA.Utilities.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/PsdPlugin.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UniTask.Addressables.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UniTask.DOTween.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UniTask.Linq.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UniTask.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Animation.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Animation.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Aseprite.Common.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Aseprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Common.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.IK.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.IK.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.PixelPerfect.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.PixelPerfect.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Psdimporter.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.SpriteShape.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Tilemap.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Tilemap.Extras.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Tilemap.Extras.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Addressables.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Addressables.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Profiling.Core.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.ResourceManager.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.ScriptableBuildPipeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.ScriptableBuildPipeline.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Sysroot.Linux_x86_64.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.SysrootPackage.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityUIExtensions.editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityUIExtensions.examples.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityUIExtensions.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityWebSocket.Editor.ref.dll"
-r:"Library/Bee/artifacts/2000b0aE.dag/UnityWebSocket.Runtime.ref.dll"
-analyzer:"D:/software/unity3d/6000.1.3f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/software/unity3d/6000.1.3f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"D:/software/unity3d/6000.1.3f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/Scripts/BasicGridAdapter.cs"
"Assets/src/Addressable/AddressableManager.cs"
"Assets/src/Addressable/AssetLoader.cs"
"Assets/src/Addressable/AssetPreloader.cs"
"Assets/src/Addressable/SceneLoader.cs"
"Assets/src/command/PopUpWinCommand.cs"
"Assets/src/controls/anime/DOTweenMovieClip.cs"
"Assets/src/controls/anime/SimpleMovieClip.cs"
"Assets/src/controls/border/GoldAreaBorderPanel.cs"
"Assets/src/controls/border/GoldBorderPanel.cs"
"Assets/src/controls/border/RightCornerBorderPanel.cs"
"Assets/src/controls/border/RoundCornerBorderPanel.cs"
"Assets/src/controls/button/BorderDropShadowButton.cs"
"Assets/src/controls/button/CommonButton.cs"
"Assets/src/controls/button/CommonToggleButton.cs"
"Assets/src/controls/button/CommonToggleButtonBar.cs"
"Assets/src/controls/button/CommonToggleButtonBasic.cs"
"Assets/src/controls/button/ConfirmButton.cs"
"Assets/src/controls/button/IToggleButtonBasic.cs"
"Assets/src/controls/button/LabelButton.cs"
"Assets/src/controls/button/MenuButton.cs"
"Assets/src/controls/button/SimpleButton.cs"
"Assets/src/controls/button/SmallButton.cs"
"Assets/src/controls/button/ToggleButtonBarBasic.cs"
"Assets/src/controls/button/ToggleButtonBasic.cs"
"Assets/src/controls/button/ToggleButtonSelected.cs"
"Assets/src/controls/button/ToggleButtonUnselected.cs"
"Assets/src/controls/ButtonLabelPlacement.cs"
"Assets/src/controls/checkBox/CheckBox.cs"
"Assets/src/controls/checkBox/CommonCheckBox.cs"
"Assets/src/controls/checkBox/CommonCheckBoxBasic.cs"
"Assets/src/controls/comboBox/ComboBox.cs"
"Assets/src/controls/comboBox/CommonComboBox.cs"
"Assets/src/controls/container/CommonImageLoader.cs"
"Assets/src/controls/container/CommonViewStack.cs"
"Assets/src/controls/container/GoldBorderUILoader.cs"
"Assets/src/controls/container/RightCornerBorderUILoader.cs"
"Assets/src/controls/container/RoundCornerBorderUILoader.cs"
"Assets/src/controls/container/ToggleContent.cs"
"Assets/src/controls/container/UILoader.cs"
"Assets/src/controls/display/ScaleSprite.cs"
"Assets/src/controls/image/TransparentRaycastImage.cs"
"Assets/src/controls/itemRenderer/RightCornerCountUILoader.cs"
"Assets/src/controls/label/CommonTextMeshPro.cs"
"Assets/src/controls/list/CommonListBasic.cs"
"Assets/src/controls/list/SimpleList.cs"
"Assets/src/controls/list/SimpleListItem.cs"
"Assets/src/controls/menu/GameMenuItem.cs"
"Assets/src/controls/menu/GamePlayerMenu.cs"
"Assets/src/controls/NumericStepper.cs"
"Assets/src/controls/numericStepper/CommonNumericStepper.cs"
"Assets/src/controls/numericStepper/CommonNumericStepperBasic.cs"
"Assets/src/controls/scrollPane/ItemData.cs"
"Assets/src/controls/scrollPane/ItemUI.cs"
"Assets/src/controls/scrollPane/OptimizedScrollView.cs"
"Assets/src/controls/scrollPane/SimpleScrollView.cs"
"Assets/src/controls/TextArea.cs"
"Assets/src/controls/textArea/CommonTextArea.cs"
"Assets/src/controls/textArea/CommonTextAreaBasic.cs"
"Assets/src/controls/TextInput.cs"
"Assets/src/controls/textInput/CommonTextInput.cs"
"Assets/src/controls/textInput/FixedCaretInputField.cs"
"Assets/src/controls/tooltip/ToolTip.cs"
"Assets/src/controls/tooltip/ToolTipTrigger.cs"
"Assets/src/controls/uiLoader/CommonUILoader.cs"
"Assets/src/controls/uiLoader/CommonUILoaderBasic.cs"
"Assets/src/core/CoroutineHelper.cs"
"Assets/src/core/InvalidationType.cs"
"Assets/src/core/UIComponent.cs"
"Assets/src/data/DataProvider.cs"
"Assets/src/data/HeroStaticData.cs"
"Assets/src/data/PopupwinData.cs"
"Assets/src/data/SoldierInfo.cs"
"Assets/src/events/ComponentEvent.cs"
"Assets/src/events/DataChangeEvent.cs"
"Assets/src/events/DataChangeType.cs"
"Assets/src/events/EventManager.cs"
"Assets/src/events/EventTriggerButtonController.cs"
"Assets/src/events/IEventManager.cs"
"Assets/src/events/MainButtonEvent.cs"
"Assets/src/events/MessageBus.cs"
"Assets/src/events/PopupWinEvent.cs"
"Assets/src/events/PopWinEvent.cs"
"Assets/src/events/TopPanelEvent.cs"
"Assets/src/events/UnityEventManager.cs"
"Assets/src/manager/Alert.cs"
"Assets/src/manager/AnimeFrameManager.cs"
"Assets/src/manager/CursorManager.cs"
"Assets/src/manager/DataBufferManager.cs"
"Assets/src/manager/InputSystemManager.cs"
"Assets/src/manager/LanguageManager.cs"
"Assets/src/manager/LoginManager.cs"
"Assets/src/manager/MediatorManager.cs"
"Assets/src/manager/PopUpWinManager.cs"
"Assets/src/manager/PrefabManager.cs"
"Assets/src/manager/ProxyManager.cs"
"Assets/src/manager/ResPathManager.cs"
"Assets/src/manager/ToolTipManager.cs"
"Assets/src/mediator/main/MainButtonRegister.cs"
"Assets/src/mediator/main/MainLoaderMediator.cs"
"Assets/src/mediator/Mediator.cs"
"Assets/src/mediator/popupwin/CastleFightWinCom.cs"
"Assets/src/mediator/popupwin/feature/FeatureTools.cs"
"Assets/src/mediator/popupwin/heroInfo/EquipmentBorder.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroBodyView.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroEquipmentMediator.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroEquipmentView.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroLookWinMediator.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroPointMediator.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroSkillMediator.cs"
"Assets/src/mediator/popupwin/heroInfo/HeroSkillView.cs"
"Assets/src/mediator/popupwin/kingdom/kingdomCom/LockIcon.cs"
"Assets/src/mediator/popupwin/kingdom/SeegoodCell.cs"
"Assets/src/mediator/popupwin/kingdom/SeegoodsWinMediator.cs"
"Assets/src/mediator/server/ServerMediator.cs"
"Assets/src/model/LoginModelLocator.cs"
"Assets/src/model/MainModelLocator.cs"
"Assets/src/proxy/BuildManageProxy.cs"
"Assets/src/proxy/ChargeShopProxy.cs"
"Assets/src/proxy/ChatProxy.cs"
"Assets/src/proxy/EquipmentProxy.cs"
"Assets/src/proxy/FeatureProxy.cs"
"Assets/src/proxy/GoodsProxy.cs"
"Assets/src/proxy/HeroOutInfoProxy.cs"
"Assets/src/proxy/MainProxy.cs"
"Assets/src/proxy/ObjectPropertyProxy.cs"
"Assets/src/proxy/ScreenProxy.cs"
"Assets/src/proxy/SkillProxy.cs"
"Assets/src/proxy/SocketProxy.cs"
"Assets/src/proxy/StakeProxy.cs"
"Assets/src/shader/UltimateEdgeFix.cs"
"Assets/src/socket/pb/BuildListVO.cs"
"Assets/src/socket/pb/BuildVO.cs"
"Assets/src/socket/pb/CastleVO.cs"
"Assets/src/socket/pb/ChatProxy.cs"
"Assets/src/socket/pb/EquipmentProxy.cs"
"Assets/src/socket/pb/EquipmentVO.cs"
"Assets/src/socket/pb/GameObjectVO.cs"
"Assets/src/socket/pb/GoodsProxy.cs"
"Assets/src/socket/pb/HeroVO.cs"
"Assets/src/socket/pb/Login.cs"
"Assets/src/socket/pb/Message.cs"
"Assets/src/socket/pb/ObjectPropertyProxy.cs"
"Assets/src/socket/pb/PlayerVO.cs"
"Assets/src/socket/pb/ServerMediator.cs"
"Assets/src/socket/pb/SkillProxy.cs"
"Assets/src/socket/WebSocketManager.cs"
"Assets/src/test/TestLogin.cs"
"Assets/src/utils/ArmyPowerUtils.cs"
"Assets/src/utils/BuildNameUtils.cs"
"Assets/src/utils/CConfig.cs"
"Assets/src/utils/ChatUtils.cs"
"Assets/src/utils/DebugUtils.cs"
"Assets/src/utils/FilterEffectUtils.cs"
"Assets/src/utils/GameObjectUtils.cs"
"Assets/src/utils/GoodsInfoBindAsync.cs"
"Assets/src/utils/HerosProTipsUtils.cs"
"Assets/src/utils/KeyUtils.cs"
"Assets/src/utils/list/IListPage.cs"
"Assets/src/utils/list/ListPage.cs"
"Assets/src/utils/ListUtils.cs"
"Assets/src/utils/MaxUtils.cs"
"Assets/src/utils/ModuleUtils.cs"
"Assets/src/utils/NumberUtils.cs"
"Assets/src/utils/popupwin/CurrentCastleIdUtils.cs"
"Assets/src/utils/popupwin/GoldSoldierPanel.cs"
"Assets/src/utils/popupwin/ISoldierBox.cs"
"Assets/src/utils/popupwin/PlayerIdUtils.cs"
"Assets/src/utils/popupwin/SolidierNumPanelManager.cs"
"Assets/src/utils/RankUtils.cs"
"Assets/src/utils/SecurityUtils.cs"
"Assets/src/utils/ServiceUtils.cs"
"Assets/src/utils/SetHerolevelUtils.cs"
"Assets/src/utils/SkillNameUtils.cs"
"Assets/src/utils/SoldierRaceUtils.cs"
"Assets/src/utils/StageUtils.cs"
"Assets/src/utils/StringUtils.cs"
"Assets/src/utils/TimeUtils.cs"
"Assets/src/utils/ToolTipUtils.cs"
"Assets/src/utils/XMLUtils.cs"
"Assets/src/view/component/CastleListButton.cs"
"Assets/src/view/component/equ/EquBtn.cs"
"Assets/src/view/component/ImageListButtonBar.cs"
"Assets/src/view/login/component/login_input_field.cs"
"Assets/src/view/main/ApplicationFacade.cs"
"Assets/src/view/main/bottom/BottomPanel.cs"
"Assets/src/view/main/bottom/BottomPanelMediator.cs"
"Assets/src/view/main/bottom/CastleGouZi.cs"
"Assets/src/view/main/bottom/component/BottomCastleNameText.cs"
"Assets/src/view/main/bottom/component/ChatPage.cs"
"Assets/src/view/main/bottom/component/InnerCenterButtonInfo.cs"
"Assets/src/view/main/bottom/heroPage/HeroItemRenderer.cs"
"Assets/src/view/main/bottom/heroPage/HeroPanel.cs"
"Assets/src/view/main/bottom/MainLeftTopMsg.cs"
"Assets/src/view/main/bottom/soldierPage/SoldierPanel.cs"
"Assets/src/view/main/component/CommonImageButtonController.cs"
"Assets/src/view/main/component/CommonSpriteButtonController.cs"
"Assets/src/view/main/GeneralMoveScript.cs"
"Assets/src/view/main/PlayerPage/PlayerPanel.cs"
"Assets/src/view/main/top/buildname/BuildNameLabel.cs"
"Assets/src/view/main/top/buildname/BuildNameLabelManager.cs"
"Assets/src/view/main/top/buildname/IBuildNameLabel.cs"
"Assets/src/view/main/top/CastleScenePrefab.cs"
"Assets/src/view/main/top/component/CastleBuildImageButton.cs"
"Assets/src/view/main/top/component/CastleBuildSpriteRenderButton.cs"
"Assets/src/view/main/top/TopPanel.cs"
"Assets/src/view/main/top/TopPanelMediator.cs"
"Assets/src/view/popupwin/basic/AlertDesktop.cs"
"Assets/src/view/popupwin/basic/AlertWindow.cs"
"Assets/src/view/popupwin/basic/AlertWindow_AlertBackground.cs"
"Assets/src/view/popupwin/basic/IPopUpWin.cs"
"Assets/src/view/popupwin/basic/PopupWinFrame.cs"
"Assets/src/view/popupwin/loader/LoaderProgressWin.cs"
"Assets/src/vo/Alcazarlist/CastleListVO.cs"
"Assets/src/vo/Alcazarlist/ResourceListVO.cs"
"Assets/src/vo/barracks/TrainingSodierVO.cs"
"Assets/src/vo/battle/BattleCommandVO.cs"
"Assets/src/vo/battle/BattleVO.cs"
"Assets/src/vo/build/BuildListVO.cs"
"Assets/src/vo/gameObject/ArmyPortVO.cs"
"Assets/src/vo/gameObject/BabelVO.cs"
"Assets/src/vo/gameObject/BattleBuildVO.cs"
"Assets/src/vo/gameObject/BossVO.cs"
"Assets/src/vo/gameObject/BuildVO.cs"
"Assets/src/vo/gameObject/CastleVO.cs"
"Assets/src/vo/gameObject/EquipmentVO.cs"
"Assets/src/vo/gameObject/ExclusiveBoss.cs"
"Assets/src/vo/gameObject/FieldBuildVO.cs"
"Assets/src/vo/gameObject/GameObjectVO.cs"
"Assets/src/vo/gameObject/HeroVO.cs"
"Assets/src/vo/gameObject/KingCastleVO.cs"
"Assets/src/vo/gameObject/NaturalBuildVO.cs"
"Assets/src/vo/gameObject/OreMineVO.cs"
"Assets/src/vo/gameObject/OwnedNaturalVO.cs"
"Assets/src/vo/gameObject/PlayerVO.cs"
"Assets/src/vo/gameObject/PresentVO.cs"
"Assets/src/vo/gameObject/PrestigeVO.cs"
"Assets/src/vo/gameObject/SkillVO.cs"
"Assets/src/vo/gameObject/SoldierVO.cs"
"Assets/src/vo/gameObject/TransmitDoorVO.cs"
"Assets/src/vo/gameObject/WorldItemVO.cs"
"Assets/src/vo/gameObject/WorldNPCVO.cs"
"Assets/src/vo/gameObject/WorldResourceVO.cs"
"Assets/src/vo/guild/GuildVO.cs"
"Assets/src/vo/guild/ProvinceVO.cs"
"Assets/src/vo/login/LoginData.cs"
"Assets/src/vo/login/MapInfoVO.cs"
"Assets/src/vo/main/NetSpeedStateVO.cs"
"Assets/src/vo/OpenWinVO.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/AnimationHelper.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/AutoSetAnchorPosForIphonex.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/DragChangSizeScript.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/DragEventForward.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/DragEventHelper.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/DragEventHelperEx.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/FPSDisplay.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/OneDirectionDragHelper.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/RotateScript.cs"
"Assets/SuperScrollView/Demo/Scripts/Base/TweenHelper.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanel.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelDelete.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGallery.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGridView.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGridViewDelete.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGridViewLoad.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelLoad.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelMenu.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelMenuList.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelNested.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelNestedSimple.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelSpecial.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelSpecialDelete.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelSpecialLoad.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelStaggeredView.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelTreeView.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelTreeViewSimple.cs"
"Assets/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelTreeViewSticky.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/AnimationType.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/ChatMsgDataSourceMgr.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/ContentFitterItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/DataSourceMgr.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/DescList.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/DraggableItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/ExpandAnimationType.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/ItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/ItemDataBase.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/NestedItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/NestedSimpleItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/SimpleExpandItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/SimpleItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/TreeViewDataSourceMgr.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/TreeViewItemCountMgr.cs"
"Assets/SuperScrollView/Demo/Scripts/DataSource/TreeViewItemData.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/AddAnimationItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseHorizontalItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseHorizontalItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseHorizontalToggleItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseHorizontalToggleItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseRowColItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseRowColItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseVerticalItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseVerticalItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseVerticalLineItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/BaseVerticalLineItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ChatViewItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ContentFitterItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/DeleteAnimationItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/DraggableHorizonalItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/DraggableVerticalItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ExpandAnimationItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ExpandItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/GalleryHorizontalItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/GalleryVerticalItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/IconItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/IconItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/IconTextDescItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/IconTextDescItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/IconTextItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/IconTextItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ImageItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ImageItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/InputFieldItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/InputFieldItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/LoadClickItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/LoadComplexItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/LoadItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/NestedGridViewLeftRightItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/NestedGridViewTopBottomItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/NestedLeftRightItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/NestedSimpleGridViewTopBottomItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/NestedSimpleLeftRightItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/NestedTopBottomItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/PageViewItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SimpleItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SimpleItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SimpleLoadItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SliderComplexItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SliderComplexItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SliderItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SliderItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/SpinPickerItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/TextDescRowColItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/TextDescRowColItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ToggleItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ToggleItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ToggleRowColItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/ToggleRowColItemList.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/TreeViewItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/TreeViewItemHead.cs"
"Assets/SuperScrollView/Demo/Scripts/Item/TreeViewSimpleItem.cs"
"Assets/SuperScrollView/Demo/Scripts/Res/ResManager.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/Chat/ChatViewChangeViewportHeightScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/Chat/ChatViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewFadeLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewFadeTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/Gallery/GalleryHorizontalDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/Gallery/GalleryVerticalDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewClickLoadMoreDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewComplexDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewDiagonalDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewDiagonalSelectDeleteDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewMultiplePrefabDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSelectDeleteDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSimpleDemo.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSimpleDiagonalDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSimpleFilterDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewBottomToTopDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewClickLoadMoreDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewContentFitterDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewExpandDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewFilterDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewMultiplePrefabDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewMultiplePrefabLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewPullDownRefreshDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewPullDownRefreshOrPullUpLoadDemo.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewPullUpLoadMoreDemo.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewRightToLeftDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSelectDeleteDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSimpleDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSimpleLoadMoreDemo.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSimpleLoopDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListViewAnimation/ListViewAddAnimationDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListViewAnimation/ListViewDeleteAnimationDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ListViewAnimation/ListViewExpandAnimationDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedGridViewLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedGridViewTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedListViewLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedListViewTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedSimpleGridViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedSimpleListViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedSimpleSpecialGridViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/PageView/PageViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/PageView/PageViewSimpleDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ResponsiveView/ResponsiveViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/ResponsiveView/ResponsiveViewRefreshLoadDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewFeatureDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewPullDownRefreshDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewPullUpLoadMoreDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewSelectDeleteDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewSimpleLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewSimpleTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpinView/SpinDatePickerDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpinView/SpinDateTimePickerDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/SpinView/SpinTimePickerDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewMoveToItemDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewSimpleLeftToRightDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewSimpleTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewTopToBottomDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewAddAndRemoveDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewSimpleDemoScript.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewWithChildIndentDemo.cs"
"Assets/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewWithStickyHeadDemoScript.cs"
"Assets/SuperScrollView/Scripts/Common/ClickEventListener.cs"
"Assets/SuperScrollView/Scripts/Common/CommonDefine.cs"
"Assets/SuperScrollView/Scripts/Common/ItemPosMgr.cs"
"Assets/SuperScrollView/Scripts/GridView/GridItemGroup.cs"
"Assets/SuperScrollView/Scripts/GridView/LoopGridItemPool.cs"
"Assets/SuperScrollView/Scripts/GridView/LoopGridView.cs"
"Assets/SuperScrollView/Scripts/GridView/LoopGridViewItem.cs"
"Assets/SuperScrollView/Scripts/ListView/LoopListItemPool.cs"
"Assets/SuperScrollView/Scripts/ListView/LoopListView2.cs"
"Assets/SuperScrollView/Scripts/ListView/LoopListViewItem2.cs"
"Assets/SuperScrollView/Scripts/StaggeredGridView/LoopStaggeredGridView.cs"
"Assets/SuperScrollView/Scripts/StaggeredGridView/LoopStaggeredGridViewItem.cs"
"Assets/SuperScrollView/Scripts/StaggeredGridView/StaggeredGridItemGroup.cs"
"Assets/SuperScrollView/Scripts/StaggeredGridView/StaggeredGridItemPool.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark01.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark01_UGUI.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark02.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark03.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark04.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/CameraController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ChatController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/DropdownSample.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/EnvMapAnimator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ObjectSpin.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ShaderPropAnimator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/SimpleScript.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/SkewTextExample.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TeleType.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextConsoleSimulator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextMeshProFloatingText.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextMeshSpawner.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMPro_InstructionOverlay.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_DigitValidator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_ExampleScript_01.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_FrameRateCounter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_PhoneNumberValidator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextEventCheck.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextEventHandler.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextInfoDebugTool.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextSelector_A.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextSelector_B.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_UiFrameRateCounter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexColorCycler.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexJitter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexShakeA.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexShakeB.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexZoom.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/WarpTextExample.cs"
"Assets/UnityChan/SplashScreen/Scripts/SplashScreen.cs"
"Assets/UnityWebSocket/Demo/UnityWebSocketDemo.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"