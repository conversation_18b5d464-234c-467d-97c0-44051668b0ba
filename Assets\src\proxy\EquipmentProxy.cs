using Pb;

public class EquipmentProxy
{
    public const string NAME = "EquipmentProxy";
    public const string GET_EQUIPMENT_TOOLTIP_LIST = "GET_EQUIPMENT_TOOLTIP_LIST";

    public const string EQUIPMENT_ARY_READY = "EQUIPMENT_ARY_READY";

    public const string FORMAT_EQUIPMENT_LOCK = "FORMAT_EQUIPMENT_LOCK";

    public const string MSG_ALCHEMYHUT = "MSG_ALCHEMYHUT";

    public const string MSG_GETHERO = "MSG_GETHERO";

    public const string MSG_ALCHEMYHUTEQUIP = "MSG_ALCHEMYHUTEQUIP";

    public const int TYPE_WEAPON = 0;

    public const int TYPE_ARMOR = 1;

    public const int TYPE_CAP = 2;

    public const int TYPE_SHOES = 3;

    public const int TYPE_GLOVE = 4;

    public const int TYPE_RING = 5;

    public const int TYPE_NECKLACE = 6;

    public const int getHERO = 471;

    private SocketProxy socketProxy;
    private ObjectPropertyProxy objectPropertyProxy;


    public EquipmentProxy()
    {
        socketProxy = ProxyManager.Instance.socketProxy;
        objectPropertyProxy = ProxyManager.Instance.objectPropertyProxy;
    }

    public void getHero()
    {
        Message message = new()
        {
            Action = SocketProxy.CMTS_HEROJN,
        };
        WebSocketManager.Instance.SendMessage(message);
    }

    public void getHeroData(HEROJN_Response p_data)
    {
        int isOk = p_data.IsOk;
        switch (isOk)
        {
            case 1:
                MessageBus.Publish(MSG_GETHERO, p_data.Residual);
                break;
        }
    }

}