using System.Collections.Generic;
using System.Linq;
using Pb;

public class GoodsProxy
{
    public static readonly Dictionary<int, EquipmentVO> goodInfo_map = new();

    public static Dictionary<int, string> unAtOnce_map = new();

    public static Dictionary<int, List<string>> presentMap = new();

    public static string GoToShop = LanguageManager.getProxy("CONTENT_GOTOSHOP");

    public const string MSG_LOAD_PLAYER_GOODS = "MSG_LOAD_PLAYER_GOODS";
    public const string MSG_LOAD_PLAYER_EQUIPMENT = "MSG_LOAD_PLAYER_EQUIPMENT";

    public const string NAME = "GoodsProxy";

    public const string XMLDATAREADY = "xmldataready";

    public const string FROMWHERE = "fromwhere";

    public const string SELECTGOOD = "selectGood";

    public const string PRESENTXMLREADY = "presentXMLReady";

    public const string GETGOODSSUCCESS = "getGoodsSuccess";

    public GoodsProxy()
    {
    }


    public void hasXmlData()
    {
        if (goodInfo_map.Count == 0)
        {
            return;
        }
        else
        {
            MessageBus.Publish(XMLDATAREADY);
        }

    }

    public void getPlayerGoods()
    {
        Message message = new()
        {
            Action = SocketProxy.CMTS_LOAD_PLAYER_GOODS,
        };
        WebSocketManager.Instance.SendMessage(message);
    }

    public void forgetPlayerGoods(PlayerGoodsResponse p_objData)
    {
        int result = p_objData.Result;
        if (result == 1)
        {
            List<PropSimple> props = p_objData.Props.ToList();
            MessageBus.Publish(MSG_LOAD_PLAYER_GOODS, props);
            return;
        }
    }

    public void getPlayerEquipmentBySort(int sort = 0)
    {
        Message message = new()
        {
            Action = SocketProxy.CMTS_PLAYER_EQUIPMENT,
            PlayerEquipmentReq = new()
            {
                Sort = sort
            }
        };
        WebSocketManager.Instance.SendMessage(message);
    }

    public void formatPlayerEquipment(PlayerEquipmentResponse p_data)
    {
        int result = p_data.Result;
        if (result == 1)
        {
            List<EquipmentSimple> equipment = p_data.Equipment.ToList();
            MessageBus.Publish(MSG_LOAD_PLAYER_EQUIPMENT, new List<object> { p_data.Sort, equipment });
            return;
        }
    }


    public Dictionary<int, EquipmentVO> getToolTipMap()
    {
        return goodInfo_map;
    }

    public Dictionary<int, List<string>> getPresentTipMap()
    {
        return presentMap;
    }

    public Dictionary<int, string> getUnAtOnceMap()
    {
        return unAtOnce_map;
    }

}