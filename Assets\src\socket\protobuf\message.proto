syntax = "proto3";

package pb;

import "Assets/src/socket/protobuf/login/login.proto";
import "Assets/src/socket/protobuf/proxy/ObjectPropertyProxy.proto";
import "Assets/src/socket/protobuf/proxy/GoodsProxy.proto";
import "Assets/src/socket/protobuf/proxy/ChatProxy.proto";
import "Assets/src/socket/protobuf/proxy/SkillProxy.proto";
import "Assets/src/socket/protobuf/proxy/EquipmentProxy.proto";
import "Assets/src/socket/protobuf/server/ServerMediator.proto";

message Message {
  int32 action = 1;
  oneof data {
    WelcomeMessage welcome_msg = 2;
    LoginRequest login_req = 3;
    LoginResponse login_res = 4;
    ObjectPropertyRequest objectProperty_req = 5;
    ObjectPropertyResponse objectProperty_res = 6;
    PlayerGoodsResponse playerGoods_res = 7;
    ClassPropertyRequest classProperty_req = 8;
    ChatWorldRequest chatWorld_req = 9;
    ChatWorldResponse chatWorld_res = 10;
    ChatWorldResultResponse chatWorldResult_res = 11;
    RACE_BUILD_LIST_Request RACE_BUILD_LIST_Req  = 12;
    RACE_BUILD_LIST_RESULT_Response RACE_BUILD_LIST_RESULT_Res  = 13;
    HERO_ADD_SKILL_Request heroAddSkill_req = 14;
    HERO_ADD_SKILL_Response heroAddSkill_res = 15;
    HERO_EQUIPMENT_Request heroEquipment_req = 16;
    HERO_EQUIPMENT_Response heroEquipment_res = 17;
    PlayerEquipmentRequest playerEquipment_req = 18;
    PlayerEquipmentResponse playerEquipment_res = 19;
    HEROJN_Response heroJN_res = 20;
  }
}

message WelcomeMessage {
  string content = 1;
}
